import { Component, ErrorInfo, ReactNode } from 'react';
import { AlertTriangle, RefreshCw } from 'lucide-react';

interface Props {
  children: ReactNode;
}

interface State {
  hasError: boolean;
  error?: Error;
  errorInfo?: ErrorInfo;
}

/**
 * 错误边界组件
 */
class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props);
    this.state = { hasError: false };
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error };
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('ErrorBoundary caught an error:', error, errorInfo);
    this.setState({
      error,
      errorInfo,
    });
  }

  handleReload = () => {
    // 移除页面刷新，改为组件重置
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  handleReset = () => {
    this.setState({ hasError: false, error: undefined, errorInfo: undefined });
  };

  render() {
    if (this.state.hasError) {
      return (
        <div className="h-full flex items-center justify-center bg-slate-900 p-4">
          <div className="max-w-md w-full text-center">
            <div className="mb-6">
              <AlertTriangle className="w-16 h-16 text-red-500 mx-auto mb-4" />
              <h2 className="text-xl font-semibold text-white mb-2">
                出现了一些问题
              </h2>
              <p className="text-slate-400 text-sm">
                应用遇到了意外错误，请尝试重新加载。
              </p>
            </div>

            {/* 错误详情（开发环境） */}
            {this.state.error && (
              <div className="mb-6 p-4 bg-slate-800 rounded-lg text-left">
                <h3 className="text-sm font-semibold text-red-400 mb-2">
                  错误详情:
                </h3>
                <pre className="text-xs text-slate-300 overflow-auto max-h-32">
                  {this.state.error.toString()}
                  {this.state.errorInfo?.componentStack}
                </pre>
              </div>
            )}

            {/* 操作按钮 */}
            <div className="flex gap-3 justify-center">
              <button
                onClick={this.handleReset}
                className="btn-secondary"
              >
                重试
              </button>
              <button
                onClick={this.handleReload}
                className="btn-primary"
              >
                <RefreshCw className="w-4 h-4" />
                重置组件
              </button>
            </div>

            {/* 反馈链接 */}
            <div className="mt-6 pt-4 border-t border-slate-700">
              <p className="text-xs text-slate-500">
                如果问题持续存在，请联系开发者
              </p>
            </div>
          </div>
        </div>
      );
    }

    return this.props.children;
  }
}

export default ErrorBoundary;
