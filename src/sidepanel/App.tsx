import React, { useState } from 'react';
import { Plus, Loader2, Refresh<PERSON>w, Settings } from 'lucide-react';
import { useWorkspaces } from '@/hooks/useWorkspaces';
import WorkspaceList from '@/components/WorkspaceList';
import CreateWorkspaceModal from '@/components/CreateWorkspaceModal';
import SettingsPanel from '@/components/SettingsPanel';
import Header from '@/components/Header';
import ErrorBoundary from '@/components/ErrorBoundary';
import { ToastProvider, useToast } from '@/components/Toast';
import { ToastErrorHandler } from '@/utils/errorHandler';

/**
 * 内部应用组件（使用 Toast）
 */
const AppContent: React.FC = () => {
  const {
    workspaces,
    activeWorkspace,
    settings,
    loading,
    error,
    createWorkspace,
    updateWorkspace,
    deleteWorkspace,
    switchWorkspace,
    addWebsite,
    addCurrentTabByWorkonaId,
    removeWebsite,
    updateWebsite,
    reorderWorkspaces,
    reorderWebsites,
    updateSettings,
    reload,
  } = useWorkspaces();

  const [showCreateModal, setShowCreateModal] = useState(false);
  const [showSettings, setShowSettings] = useState(false);

  // Toast 错误处理
  const { showError } = useToast();
  const errorHandler = new ToastErrorHandler(showError);

  /**
   * 处理创建工作区
   */
  const handleCreateWorkspace = async (name: string, options?: {
    icon?: string;
    color?: string;
    activate?: boolean;
    addCurrentTabs?: boolean;
  }) => {
    try {
      await createWorkspace(name, options);
      setShowCreateModal(false);
    } catch (err) {
      errorHandler.handleWorkspaceError(err, '创建');
    }
  };

  /**
   * 处理工作区切换
   */
  const handleSwitchWorkspace = async (workspaceId: string) => {
    try {
      await switchWorkspace(workspaceId);
    } catch (err) {
      errorHandler.handleWorkspaceError(err, '切换');
    }
  };

  /**
   * 处理添加当前标签页（基于Workona ID血缘关系）
   */
  const handleAddCurrentTab = async (workspaceId: string) => {
    try {
      // 获取当前活跃标签页
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length > 0) {
        const tab = tabs[0];
        if (tab.url && !tab.url.startsWith('chrome://') && tab.id) {
          // 使用基于Workona ID血缘关系的方法直接处理当前标签页
          await addCurrentTabByWorkonaId(workspaceId, tab.id, {
            url: tab.url,
            title: tab.title,
            favicon: tab.favIconUrl || undefined,
          });
        }
      }
    } catch (err) {
      errorHandler.handleWebsiteError(err, '添加当前标签页');
    }
  };

  /**
   * 处理添加网站URL
   */
  const handleAddWebsiteUrl = async (workspaceId: string, url: string) => {
    try {
      await addWebsite(workspaceId, url, {
        openInNewTab: true,
      });
    } catch (err) {
      errorHandler.handleWebsiteError(err, '添加网站');
    }
  };

  /**
   * 处理更新网站
   */
  const handleUpdateWebsite = async (workspaceId: string, websiteId: string, updates: { url?: string; title?: string; isPinned?: boolean }) => {
    try {
      await updateWebsite(workspaceId, websiteId, updates);
    } catch (err) {
      errorHandler.handleWebsiteError(err, '更新网站');
    }
  };

  /**
   * 处理切换固定状态
   */
  const handleTogglePin = async (workspaceId: string, websiteId: string, isPinned: boolean) => {
    try {
      await updateWebsite(workspaceId, websiteId, { isPinned });
    } catch (err) {
      errorHandler.handleWebsiteError(err, '切换固定状态');
    }
  };

  /**
   * 处理批量固定
   */
  const handleBatchPin = async (workspaceId: string, websiteIds: string[], isPinned: boolean) => {
    try {
      // 批量更新网站的固定状态
      for (const websiteId of websiteIds) {
        await updateWebsite(workspaceId, websiteId, { isPinned });
      }
    } catch (err) {
      errorHandler.handleWebsiteError(err, '批量固定网站');
    }
  };

  /**
   * 处理批量删除（集成标签页降级机制）
   */
  const handleBatchDelete = async (workspaceId: string, websiteIds: string[]) => {
    try {
      console.log(`🗑️ 批量删除网站: ${websiteIds.length} 个`);

      // 在删除网站前，先处理相关的工作区专属标签页降级
      const { WorkonaTabManager } = await import('@/utils/workonaTabManager');
      const { StorageManager } = await import('@/utils/storage');

      for (const websiteId of websiteIds) {
        try {
          // 查找与该网站关联的工作区专属标签页
          const mappingsResult = await StorageManager.getTabIdMappings();

          if (mappingsResult.success) {
            const mappings = mappingsResult.data!;
            const relatedMappings = mappings.filter(mapping =>
              mapping.workspaceId === workspaceId &&
              mapping.websiteId === websiteId &&
              mapping.isWorkspaceCore
            );

            console.log(`🔍 网站 ${websiteId} 关联的工作区专属标签页: ${relatedMappings.length} 个`);

            // 降级每个相关的工作区专属标签页
            for (const mapping of relatedMappings) {
              const demoteResult = await WorkonaTabManager.demoteToSessionTab(mapping.workonaId);
              if (demoteResult.success) {
                console.log(`⬇️ 批量操作：成功降级标签页 ${mapping.workonaId}`);
              } else {
                console.warn(`⚠️ 批量操作：降级标签页失败 ${mapping.workonaId}`, demoteResult.error);
              }
            }
          }

          // 删除网站
          await removeWebsite(workspaceId, websiteId);
        } catch (error) {
          console.warn(`处理网站 ${websiteId} 的批量删除时出错:`, error);
          // 继续处理其他网站，不中断批量操作
        }
      }

      console.log(`✅ 批量删除完成: ${websiteIds.length} 个网站`);
    } catch (err) {
      errorHandler.handleWebsiteError(err, '批量删除网站');
    }
  };



  // 加载状态
  if (loading) {
    return (
      <div className="h-full flex items-center justify-center bg-slate-900">
        <div className="flex flex-col items-center gap-3">
          <Loader2 className="w-8 h-8 animate-spin text-blue-500" />
          <p className="text-slate-400 text-sm">加载工作区...</p>
        </div>
      </div>
    );
  }

  // 错误状态 - 使用Toast显示错误，严重错误时显示错误界面
  React.useEffect(() => {
    if (error) {
      console.error('工作区加载错误:', error);
      errorHandler.handle(error, '加载工作区');
    }
  }, [error, errorHandler]);

  // 严重错误状态：有错误且没有工作区数据且不在加载中
  if (error && workspaces.length === 0 && !loading) {
    return (
      <ErrorBoundary>
        <div className="h-full flex flex-col bg-slate-900 text-white">
          <Header
            activeWorkspace={null}
            onSettingsClick={() => setShowSettings(true)}
            onCreateWorkspaceClick={() => setShowCreateModal(true)}
          />
          <div className="flex-1 flex items-center justify-center p-4">
            <div className="text-center max-w-md">
              <div className="text-red-500 text-6xl mb-4">⚠️</div>
              <h3 className="text-lg font-semibold text-slate-200 mb-2">加载失败</h3>
              <p className="text-slate-400 text-sm mb-4 leading-relaxed">
                无法加载工作区数据，请检查网络连接或稍后重试
              </p>
              <div className="text-xs text-slate-500 mb-6 p-3 bg-slate-800 rounded border-l-2 border-red-500">
                错误详情：{error}
              </div>
              <div className="flex gap-3 justify-center">
                <button
                  onClick={reload}
                  className="btn-primary flex items-center gap-2"
                >
                  <RefreshCw className="w-4 h-4" />
                  重试
                </button>
                <button
                  onClick={() => setShowSettings(true)}
                  className="btn-secondary flex items-center gap-2"
                >
                  <Settings className="w-4 h-4" />
                  设置
                </button>
              </div>
            </div>
          </div>
        </div>
      </ErrorBoundary>
    );
  }

  return (
    <ErrorBoundary>
      <div className="h-full flex flex-col bg-slate-900 text-white">
        {/* 头部 */}
        <Header
          activeWorkspace={activeWorkspace}
          onSettingsClick={() => setShowSettings(true)}
          onCreateWorkspaceClick={() => setShowCreateModal(true)}
        />

        {/* 主内容区域 - 充满布局 */}
        <div className="flex-1 flex flex-col overflow-hidden">
          {/* 工作区列表 - 消除左右间距 */}
          <div className="flex-1 relative">
            {workspaces.length === 0 ? (
              <div className="h-full flex items-center justify-center px-4 py-8">
                <div className="text-center">
                  <div className="text-5xl mb-3">🚀</div>
                  <h3 className="text-base font-semibold text-slate-200 mb-2">
                    欢迎使用 WorkSpace Pro
                  </h3>
                  <p className="text-slate-400 text-sm mb-4">
                    创建您的第一个工作区来开始管理标签页
                  </p>
                  <button
                    onClick={() => setShowCreateModal(true)}
                    className="btn-primary text-sm mx-auto"
                  >
                    <Plus className="w-4 h-4" />
                    创建工作区
                  </button>
                </div>
              </div>
            ) : (
              <WorkspaceList
                workspaces={workspaces}
                activeWorkspaceId={activeWorkspace?.id || null}
                onSwitchWorkspace={handleSwitchWorkspace}
                onUpdateWorkspace={updateWorkspace}
                onDeleteWorkspace={deleteWorkspace}
                onAddCurrentTab={handleAddCurrentTab}
                onAddWebsiteUrl={handleAddWebsiteUrl}
                onRemoveWebsite={removeWebsite}
                onUpdateWebsite={handleUpdateWebsite}
                onReorderWorkspaces={reorderWorkspaces}
                onReorderWebsites={reorderWebsites}
                onTogglePin={handleTogglePin}
                onBatchPin={handleBatchPin}
                onBatchDelete={handleBatchDelete}
              />
            )}
          </div>
        </div>

        {/* 创建工作区模态框 */}
        {showCreateModal && (
          <CreateWorkspaceModal
            onClose={() => setShowCreateModal(false)}
            onCreate={handleCreateWorkspace}
          />
        )}

        {/* 设置面板 */}
        {showSettings && (
          <SettingsPanel
            onClose={() => setShowSettings(false)}
          />
        )}
      </div>
    </ErrorBoundary>
  );
};

/**
 * 主应用组件
 */
const App: React.FC = () => {
  return (
    <ToastProvider>
      <AppContent />
    </ToastProvider>
  );
};

export default App;
