import {
  TabInfo,
  WorkSpace,
  OperationResult,
  WorkonaTabMatchResult
} from '@/types/workspace';
import { ERROR_CODES } from './constants';
import { WorkonaTabManager } from './workonaTabManager';
import { StorageManager } from './storage';
import { WorkspaceManager } from './workspace';
import { WindowManager } from './windowManager';

/**
 * 标签页管理类
 */
export class TabManager {
  /**
   * 获取所有标签页信息
   */
  static async getAllTabs(): Promise<OperationResult<TabInfo[]>> {
    try {
      const tabs = await chrome.tabs.query({});
      const tabInfos: TabInfo[] = tabs.map(tab => ({
        id: tab.id!,
        url: tab.url || '',
        title: tab.title || '',
        favicon: tab.favIconUrl || '',
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,
        index: tab.index
      }));

      return { success: true, data: tabInfos };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 获取当前活跃标签页
   */
  static async getActiveTab(): Promise<OperationResult<TabInfo>> {
    try {
      const tabs = await chrome.tabs.query({ active: true, currentWindow: true });
      if (tabs.length === 0) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: 'No active tab found',
          },
        };
      }

      const tab = tabs[0];
      const tabInfo: TabInfo = {
        id: tab.id!,
        url: tab.url || '',
        title: tab.title || '',
        favicon: tab.favIconUrl || '',
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,
        index: tab.index
      };

      return { success: true, data: tabInfo };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get active tab',
          details: error,
        },
      };
    }
  }

  /**
   * 检查URL是否已在当前窗口的标签页中打开（工作区隔离优化版）
   */
  static async findTabByUrl(url: string): Promise<OperationResult<TabInfo | null>> {
    try {
      // 首先尝试在当前窗口中精确匹配
      let tabs = await chrome.tabs.query({ url, currentWindow: true });

      // 如果精确匹配失败，尝试在当前窗口中进行域名匹配
      if (tabs.length === 0) {
        try {
          const targetDomain = new URL(url).hostname;
          const currentWindowTabs = await chrome.tabs.query({ currentWindow: true });
          tabs = currentWindowTabs.filter(tab => {
            if (!tab.url) return false;
            try {
              const tabDomain = new URL(tab.url).hostname;
              return tabDomain === targetDomain;
            } catch {
              return false;
            }
          });
        } catch {
          // URL解析失败，返回空结果
          return { success: true, data: null };
        }
      }

      if (tabs.length === 0) {
        return { success: true, data: null };
      }

      const tab = tabs[0];
      const tabInfo: TabInfo = {
        id: tab.id!,
        url: tab.url || '',
        title: tab.title || '',
        favicon: tab.favIconUrl || '',
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,
        index: tab.index
      };

      return { success: true, data: tabInfo };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to find tab by URL',
          details: error,
        },
      };
    }
  }

  /**
   * 创建新标签页
   */
  static async createTab(url: string, pinned: boolean = false, active: boolean = true): Promise<OperationResult<TabInfo>> {
    try {
      const tab = await chrome.tabs.create({
        url,
        pinned,
        active,
      });

      const tabInfo: TabInfo = {
        id: tab.id!,
        url: tab.url || '',
        title: tab.title || '',
        favicon: tab.favIconUrl || '',
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,
        index: tab.index
      };

      return { success: true, data: tabInfo };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to create tab',
          details: error,
        },
      };
    }
  }



  /**
   * 激活标签页
   */
  static async activateTab(tabId: number): Promise<OperationResult<void>> {
    try {
      console.log(`Activating tab ${tabId}`);
      await chrome.tabs.update(tabId, { active: true });
      console.log(`Successfully activated tab ${tabId}`);
      return { success: true };
    } catch (error) {
      console.error(`Failed to activate tab ${tabId}:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to activate tab',
          details: error,
        },
      };
    }
  }

  // Workona 风格：移除固定标签页功能，完全基于 Workona ID 映射管理

  /**
   * 关闭标签页
   */
  static async closeTab(tabId: number): Promise<OperationResult<void>> {
    try {
      // 先取消固定状态
      try {
        const tab = await chrome.tabs.get(tabId);
        if (tab.pinned) {
          await chrome.tabs.update(tabId, { pinned: false });
        }
      } catch (error) {
        // 如果获取或取消固定失败，继续删除操作
        console.warn('取消标签页固定状态失败:', error);
      }

      await chrome.tabs.remove(tabId);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to close tab',
          details: error,
        },
      };
    }
  }

  /**
   * 关闭多个标签页
   */
  static async closeTabs(tabIds: number[]): Promise<OperationResult<void>> {
    try {
      // 先取消所有标签页的固定状态
      for (const tabId of tabIds) {
        try {
          const tab = await chrome.tabs.get(tabId);
          if (tab.pinned) {
            await chrome.tabs.update(tabId, { pinned: false });
          }
        } catch (error) {
          // 如果某个标签页获取或取消固定失败，继续处理其他标签页
          console.warn(`取消标签页 ${tabId} 固定状态失败:`, error);
        }
      }

      await chrome.tabs.remove(tabIds);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to close tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 获取当前窗口的所有标签页
   */
  static async getCurrentWindowTabs(): Promise<OperationResult<TabInfo[]>> {
    try {
      const tabs = await chrome.tabs.query({ currentWindow: true });
      const tabInfos: TabInfo[] = tabs.map(tab => ({
        id: tab.id!,
        url: tab.url || '',
        title: tab.title || '',
        favicon: tab.favIconUrl || '',
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,
        index: tab.index
      }));

      return { success: true, data: tabInfos };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get current window tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 获取工作区真正管理的标签页（Workona 风格：完全基于 ID 映射）
   */
  static async getWorkspaceRelatedTabs(workspace: WorkSpace): Promise<OperationResult<TabInfo[]>> {
    try {
      console.log(`🔍 查找工作区 "${workspace.name}" 的 Workona 管理标签页`);

      // 阶段1：通过 Workona ID 映射查找标签页
      const workonaTabIds = await WorkonaTabManager.getWorkspaceWorkonaTabIds(workspace.id);
      if (!workonaTabIds.success) {
        console.log(`❌ 获取工作区 Workona 标签页ID失败:`, workonaTabIds.error);
        return { success: true, data: [] }; // 返回空数组而不是错误
      }

      const relatedTabs: TabInfo[] = [];
      const validWorkonaIds: string[] = [];

      // 将 Workona ID 转换为实际标签页
      for (const workonaId of workonaTabIds.data!) {
        const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
        if (chromeIdResult.success && chromeIdResult.data) {
          try {
            // 获取标签页详细信息
            const tab = await chrome.tabs.get(chromeIdResult.data);
            if (tab) {
              relatedTabs.push({
                id: tab.id!,
                url: tab.url!,
                title: tab.title!,
                favicon: tab.favIconUrl || '',
                isPinned: tab.pinned,
                isActive: tab.active,
                windowId: tab.windowId,
                index: tab.index
              });
              validWorkonaIds.push(workonaId);
              console.log(`✅ 找到 Workona 管理的标签页: ${tab.title} (${workonaId})`);
            }
          } catch (tabError) {
            // 标签页不存在，清理无效映射
            await WorkonaTabManager.removeTabMapping(workonaId);
            console.log(`🗑️ 清理无效的 Workona ID 映射: ${workonaId}`);
          }
        } else {
          // 映射无效，清理
          await WorkonaTabManager.removeTabMapping(workonaId);
          console.log(`🗑️ 清理无效的 Workona ID 映射: ${workonaId}`);
        }
      }

      console.log(`📊 工作区 "${workspace.name}" Workona 管理的标签页: ${relatedTabs.length} 个 (有效映射: ${validWorkonaIds.length})`);
      return { success: true, data: relatedTabs };
    } catch (error) {
      console.error('获取工作区相关标签页失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get workspace related tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 获取非工作区相关的标签页（仅在当前窗口中查找）
   */
  static async getNonWorkspaceRelatedTabs(workspace: WorkSpace): Promise<OperationResult<TabInfo[]>> {
    try {
      console.log(`查找非工作区 "${workspace.name}" 相关的标签页`);

      // 只在当前窗口中查找非相关标签页
      const currentTabsResult = await this.getCurrentWindowTabs();
      if (!currentTabsResult.success) return currentTabsResult;

      const currentTabs = currentTabsResult.data!;
      const workspaceUrls = workspace.websites.map(w => w.url);

      const nonRelatedTabs = currentTabs.filter(tab =>
        !workspaceUrls.some(url => tab.url.startsWith(url))
      );

      return { success: true, data: nonRelatedTabs };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get non-workspace related tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 检查标签页是否为用户手动打开的（非工作区配置的标签页）
   */
  static async isUserOpenedTab(tabId: number): Promise<boolean> {
    try {
      // 概念性重构：通过 Workona ID 映射和元数据检查
      const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
      if (!workonaIdResult.success || !workonaIdResult.data) {
        // 无 Workona ID = 系统标签页或未分类标签页
        return true;
      }

      // 检查标签页性质
      const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);
      if (!metadataResult.success || !metadataResult.data) {
        return true;
      }

      // 根据来源判断是否为用户手动打开
      const { metadata } = metadataResult.data;
      return metadata?.source === 'user_opened';
    } catch {
      // 出错时采用保守策略，认为是用户标签页
      return true;
    }
  }

  /**
   * 获取当前应该用于新标签页的工作区（避免循环依赖）
   */
  private static async getCurrentWorkspaceForNewTab(): Promise<WorkSpace | null> {
    try {
      console.log(`🔍 [getCurrentWorkspaceForNewTab] 开始获取当前工作区...`);

      // 策略1：优先使用存储中的活跃工作区ID（最准确）
      const activeIdResult = await StorageManager.getActiveWorkspaceId();
      if (activeIdResult.success && activeIdResult.data) {
        const workspacesResult = await StorageManager.getWorkspaces();
        if (workspacesResult.success && workspacesResult.data) {
          const workspace = workspacesResult.data.find(w => w.id === activeIdResult.data);
          if (workspace) {
            console.log(`🎯 [getCurrentWorkspaceForNewTab] 通过存储的活跃工作区ID检测到当前工作区: ${workspace.name}`);
            return workspace;
          }
        }
      }

      // 策略2：检查当前窗口中其他标签页的工作区归属
      const currentTabsResult = await this.getCurrentWindowTabs();
      if (!currentTabsResult.success) {
        console.log(`❌ [getCurrentWorkspaceForNewTab] 获取当前窗口标签页失败:`, currentTabsResult.error);
        return null;
      }

      const currentTabs = currentTabsResult.data!;
      console.log(`🔍 [getCurrentWorkspaceForNewTab] 当前窗口有 ${currentTabs.length} 个标签页`);

      // 查找当前窗口中有 Workona ID 的标签页
      for (const tab of currentTabs) {
        console.log(`🔍 [getCurrentWorkspaceForNewTab] 检查标签页: ${tab.title} (${tab.url})`);
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        console.log(`🔍 [getCurrentWorkspaceForNewTab] 标签页 ${tab.id} 的 Workona ID:`, workonaIdResult);

        if (workonaIdResult.success && workonaIdResult.data) {
          const workspaceId = workonaIdResult.data.split('-')[1];
          console.log(`🔍 [getCurrentWorkspaceForNewTab] 从 Workona ID ${workonaIdResult.data} 提取工作区ID: ${workspaceId}`);

          // 获取工作区信息
          const workspacesResult = await StorageManager.getWorkspaces();
          if (workspacesResult.success && workspacesResult.data) {
            const workspace = workspacesResult.data.find(w => w.id === workspaceId);
            if (workspace) {
              console.log(`🎯 [getCurrentWorkspaceForNewTab] 通过窗口中其他标签页检测到当前工作区: ${workspace.name}`);
              return workspace;
            } else {
              console.log(`⚠️ [getCurrentWorkspaceForNewTab] 工作区 ${workspaceId} 不存在`);
            }
          }
        }
      }

      console.log(`🔍 [getCurrentWorkspaceForNewTab] 策略2失败，策略3：通过活跃工作区检测`);

      // 策略3：通过 WorkspaceSwitcher 检测当前活跃工作区
      try {
        const { WorkspaceSwitcher } = await import('./workspaceSwitcher');
        const activeWorkspaceResult = await WorkspaceSwitcher.detectActiveWorkspace();

        if (activeWorkspaceResult.success && activeWorkspaceResult.data) {
          const workspace = activeWorkspaceResult.data;
          console.log(`🎯 [getCurrentWorkspaceForNewTab] 通过 WorkspaceSwitcher 检测到当前工作区: ${workspace.name}`);
          return workspace;
        } else {
          console.log(`⚠️ [getCurrentWorkspaceForNewTab] WorkspaceSwitcher 未检测到活跃工作区`);
        }
      } catch (error) {
        console.warn(`⚠️ [getCurrentWorkspaceForNewTab] 策略3失败:`, error);
      }

      console.log(`🔍 [getCurrentWorkspaceForNewTab] 策略3失败，策略4：使用默认工作区`);

      // 策略4：如果没有检测到活跃工作区，使用第一个可用的工作区
      try {
        const workspacesResult = await StorageManager.getWorkspaces();
        if (workspacesResult.success && workspacesResult.data && workspacesResult.data.length > 0) {
          const firstWorkspace = workspacesResult.data[0];
          console.log(`🎯 [getCurrentWorkspaceForNewTab] 使用默认工作区: ${firstWorkspace.name}`);
          return firstWorkspace;
        }
      } catch (error) {
        console.warn(`⚠️ [getCurrentWorkspaceForNewTab] 策略4失败:`, error);
      }

      // 策略5：创建临时工作区归属
      console.log(`🎯 [getCurrentWorkspaceForNewTab] 所有策略失败，将创建临时工作区归属`);
      return null;
    } catch (error) {
      console.error('❌ [getCurrentWorkspaceForNewTab] 获取当前工作区失败:', error);
      return null;
    }
  }

  /**
   * 自动为新标签页创建会话临时 Workona ID（概念性重构）
   */
  static async autoClassifyNewTab(tabId: number, url: string): Promise<OperationResult<void>> {
    try {
      console.log(`🎯 [autoClassifyNewTab] 开始自动分类标签页: ID=${tabId}, URL=${url}`);

      // 使用新的3分类系统判断系统页面
      const { TabClassificationUtils } = await import('./workspaceSwitcher');
      if (TabClassificationUtils.isSystemTab(url)) {
        console.log(`🚫 [autoClassifyNewTab] 跳过系统页面: ${url}`);
        return { success: true }; // 系统页面不需要分类
      }

      // 检查是否已有 Workona ID
      const existingResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
      console.log(`🔍 [autoClassifyNewTab] 检查现有 Workona ID:`, existingResult);

      if (existingResult.success && existingResult.data) {
        console.log(`ℹ️ [autoClassifyNewTab] 标签页已有 Workona ID: ${existingResult.data}，跳过分类`);
        return { success: true }; // 已有映射，无需重复创建
      }

      // 获取当前活跃工作区（避免循环依赖）
      console.log(`🔍 [autoClassifyNewTab] 开始获取当前工作区...`);
      const activeWorkspace = await this.getCurrentWorkspaceForNewTab();
      console.log(`🔍 [autoClassifyNewTab] 检查当前工作区:`, activeWorkspace);

      if (!activeWorkspace) {
        console.warn(`⚠️ [autoClassifyNewTab] 无当前工作区，但仍为用户标签页创建临时映射: ${url}`);

        // 即使没有活跃工作区，也要为用户标签页创建一个临时的Workona ID
        // 使用特殊的工作区ID "temp-user-session" 来标识这些标签页
        const tempWorkspaceId = 'temp-user-session';
        const workonaId = WorkonaTabManager.generateWorkonaTabId(tempWorkspaceId);
        console.log(`🆔 为无工作区的用户标签页生成临时 Workona ID: ${workonaId}`);

        const mappingResult = await WorkonaTabManager.createTabIdMapping(
          workonaId,
          tabId,
          tempWorkspaceId,
          undefined, // 无对应的网站配置ID
          {
            isWorkspaceCore: false, // 标记为会话临时标签页
            source: 'user_opened'
          }
        );

        if (mappingResult.success) {
          console.log(`✅ [autoClassifyNewTab] 成功为无工作区标签页创建临时映射: ${url} -> ${workonaId}`);
        } else {
          console.error(`❌ [autoClassifyNewTab] 创建临时映射失败:`, mappingResult.error);
        }

        return { success: mappingResult.success, error: mappingResult.error };
      }
      console.log(`✅ 找到活跃工作区: ${activeWorkspace.name} (ID: ${activeWorkspace.id})`);

      // 为用户临时打开的标签页创建会话临时 Workona ID
      const workonaId = WorkonaTabManager.generateWorkonaTabId(activeWorkspace.id);
      console.log(`🆔 生成 Workona ID: ${workonaId}`);

      const mappingResult = await WorkonaTabManager.createTabIdMapping(
        workonaId,
        tabId,
        activeWorkspace.id,
        undefined, // 无对应的网站配置ID
        {
          isWorkspaceCore: false, // 标记为会话临时标签页
          source: 'user_opened'
        }
      );

      console.log(`🔍 创建映射结果:`, mappingResult);

      if (mappingResult.success) {
        console.log(`✨ [autoClassifyNewTab] 自动为用户标签页创建会话临时 Workona ID: ${workonaId} (${url})`);

        // 验证映射是否真的创建成功
        const verifyResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
        console.log(`🔍 [autoClassifyNewTab] 验证映射创建结果:`, verifyResult);

        if (verifyResult.success && verifyResult.data) {
          console.log(`✅ [autoClassifyNewTab] 映射验证成功: ${verifyResult.data}`);
        } else {
          console.error(`❌ [autoClassifyNewTab] 映射验证失败:`, verifyResult);
        }
      } else {
        console.error(`❌ [autoClassifyNewTab] 创建 Workona ID 映射失败:`, mappingResult.error);
      }

      return { success: mappingResult.success, error: mappingResult.error };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to auto-classify new tab',
          details: error,
        },
      };
    }
  }

  /**
   * 获取当前工作区中用户自行打开的标签页（Workona 风格：基于 ID 映射）
   */
  static async getUserOpenedTabs(workspace: WorkSpace): Promise<OperationResult<TabInfo[]>> {
    try {
      console.log(`🔍 查找工作区 "${workspace.name}" 中用户自行打开的标签页 (Workona 风格)`);

      // 获取当前窗口的所有标签页
      const currentTabsResult = await this.getCurrentWindowTabs();
      if (!currentTabsResult.success) return currentTabsResult;

      const currentTabs = currentTabsResult.data!;
      const userOpenedTabs: TabInfo[] = [];

      // 通过 Workona ID 映射检查每个标签页
      for (const tab of currentTabs) {
        // 使用新的3分类系统排除系统页面
        const { TabClassificationUtils } = await import('./workspaceSwitcher');
        if (TabClassificationUtils.isSystemTab(tab.url)) {
          continue;
        }

        // 检查是否有 Workona ID 映射
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (!workonaIdResult.success || !workonaIdResult.data) {
          // 没有 Workona ID 映射，认为是用户自行打开的标签页
          userOpenedTabs.push(tab);
          console.log(`👤 发现用户自行打开的标签页: ${tab.title} (${tab.url})`);
        } else {
          console.log(`🏢 跳过 Workona 管理的标签页: ${tab.title} (${workonaIdResult.data})`);
        }
      }

      console.log(`📊 工作区 "${workspace.name}" 中用户自行打开的标签页共 ${userOpenedTabs.length} 个`);
      return { success: true, data: userOpenedTabs };
    } catch (error) {
      console.error('获取用户自行打开的标签页失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get user opened tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 获取当前工作区中配置的标签页（工作区设置中明确添加的网站标签页）
   */
  static async getWorkspaceConfiguredTabs(workspace: WorkSpace): Promise<OperationResult<TabInfo[]>> {
    try {
      // 获取当前窗口的所有标签页
      const currentTabsResult = await this.getCurrentWindowTabs();
      if (!currentTabsResult.success) return currentTabsResult;

      const currentTabs = currentTabsResult.data!;
      const workspaceUrls = workspace.websites.map(w => w.url);

      // 过滤出属于工作区配置的标签页
      const configuredTabs = currentTabs.filter(tab => {
        return workspaceUrls.some(url => tab.url.startsWith(url));
      });

      console.log(`工作区 "${workspace.name}" 中配置的标签页共 ${configuredTabs.length} 个`);
      return { success: true, data: configuredTabs };
    } catch (error) {
      console.error('获取工作区配置的标签页失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get workspace configured tabs',
          details: error,
        },
      };
    }
  }

  // ===== Workona 风格标签页管理方法 =====

  /**
   * 创建 Workona 风格标签页
   */
  static async createWorkonaTab(options: {
    workspaceId: string;
    url: string;
    title?: string;
    isPinned?: boolean;
    isActive?: boolean;
    websiteId?: string;
  }): Promise<OperationResult<{ tabInfo: TabInfo; workonaId: string }>> {
    try {
      console.log(`🚀 创建 Workona 标签页: ${options.url} (工作区: ${options.workspaceId})`);

      // 生成 Workona ID
      const workonaId = WorkonaTabManager.generateWorkonaTabId(options.workspaceId);

      // 创建 Chrome 标签页
      const tab = await chrome.tabs.create({
        url: options.url,
        pinned: options.isPinned ?? true,
        active: options.isActive ?? false,
      });

      if (!tab.id) {
        throw new Error('Failed to create Chrome tab');
      }

      // 创建标签页信息
      const tabInfo: TabInfo = {
        id: tab.id,
        url: tab.url || options.url,
        title: tab.title || options.title || '',
        favicon: tab.favIconUrl || '',
        isPinned: tab.pinned,
        isActive: tab.active,
        windowId: tab.windowId,
        index: tab.index,
      };

      // 创建 ID 映射
      const mappingResult = await WorkonaTabManager.createTabIdMapping(
        workonaId,
        tab.id,
        options.workspaceId,
        options.websiteId
      );

      if (!mappingResult.success) {
        // 如果映射创建失败，尝试关闭标签页
        try {
          await chrome.tabs.remove(tab.id);
        } catch (cleanupError) {
          console.warn('清理失败的标签页时出错:', cleanupError);
        }
        return { success: false, error: mappingResult.error };
      }

      console.log(`✅ 成功创建 Workona 标签页: ${workonaId} <-> ${tab.id}`);
      return {
        success: true,
        data: { tabInfo, workonaId }
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to create Workona tab',
          details: error,
        },
      };
    }
  }

  /**
   * 获取标签页的 Workona ID
   */
  static async getTabWorkonaId(chromeTabId: number): Promise<OperationResult<string | null>> {
    return await WorkonaTabManager.getWorkonaIdByChromeId(chromeTabId);
  }

  /**
   * 通过 Workona ID 获取标签页信息
   */
  static async getTabByWorkonaId(workonaId: string): Promise<OperationResult<TabInfo | null>> {
    try {
      const chromeIdResult = await WorkonaTabManager.getChromeIdByWorkonaId(workonaId);
      if (!chromeIdResult.success || !chromeIdResult.data) {
        return { success: true, data: null };
      }

      const chromeId = chromeIdResult.data;

      try {
        const tab = await chrome.tabs.get(chromeId);
        const tabInfo: TabInfo = {
          id: tab.id!,
          url: tab.url || '',
          title: tab.title || '',
          favicon: tab.favIconUrl || '',
          isPinned: tab.pinned,
          isActive: tab.active,
          windowId: tab.windowId,
          index: tab.index,
        };

        return { success: true, data: tabInfo };
      } catch (tabError) {
        // 标签页不存在，清理映射
        await WorkonaTabManager.removeTabMapping(workonaId);
        return { success: true, data: null };
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to get tab by Workona ID',
          details: error,
        },
      };
    }
  }

  /**
   * 同步工作区的 Workona 标签页映射
   */
  static async syncWorkspaceTabMappings(workspaceId: string): Promise<OperationResult<void>> {
    try {
      console.log(`🔄 同步工作区 ${workspaceId} 的标签页映射...`);

      // 获取工作区的所有 Workona 标签页ID
      const workonaIdsResult = await WorkonaTabManager.getWorkspaceWorkonaTabIds(workspaceId);
      if (!workonaIdsResult.success) {
        return { success: false, error: workonaIdsResult.error };
      }

      const workonaIds = workonaIdsResult.data!;
      let syncedCount = 0;
      let cleanedCount = 0;

      // 检查每个 Workona 标签页是否仍然存在
      for (const workonaId of workonaIds) {
        const tabResult = await this.getTabByWorkonaId(workonaId);
        if (!tabResult.success) {
          continue;
        }

        if (tabResult.data) {
          syncedCount++;
        } else {
          cleanedCount++;
        }
      }

      console.log(`✅ 工作区 ${workspaceId} 标签页映射同步完成: ${syncedCount} 个有效, ${cleanedCount} 个已清理`);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to sync workspace tab mappings',
          details: error,
        },
      };
    }
  }
}



/**
 * 工作区标签页内容匹配系统
 */
export class WorkspaceTabContentMatcher {


  /**
   * 检查标签页是否与工作区网站匹配（Workona 风格：完全基于 ID 映射和 URL 匹配）
   */
  static async isWorkspaceTab(tab: TabInfo): Promise<WorkonaTabMatchResult> {
    try {
      // 使用新的3分类系统排除系统页面
      const { TabClassificationUtils } = await import('./workspaceSwitcher');
      if (TabClassificationUtils.isSystemTab(tab.url)) {
        return {
          isMatch: false,
          confidence: 0,
          matchType: 'none'
        };
      }

      // 阶段1：优先通过 Workona ID 映射查找（最高优先级）
      const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
      let workonaTabId: string | undefined;

      if (workonaIdResult.success && workonaIdResult.data) {
        workonaTabId = workonaIdResult.data;
        // 从 Workona ID 中提取工作区ID：t-{workspaceId}-{uuid}
        const workspaceId = workonaTabId.split('-')[1];

        console.log(`🔗 通过 Workona ID 映射找到工作区标签页: ${tab.url} -> ${workonaTabId} (工作区: ${workspaceId})`);

        // 验证工作区是否仍然存在
        const workspacesResult = await StorageManager.getWorkspaces();
        if (workspacesResult.success && workspacesResult.data) {
          const matchingWorkspace = workspacesResult.data.find(w => w.id === workspaceId);

          if (matchingWorkspace) {
            // 获取标签页元数据以确定websiteId
            const metadataResult = await WorkonaTabManager.getTabMetadata(workonaTabId);
            const websiteId = metadataResult.success ? metadataResult.data?.websiteId : undefined;

            return {
              isMatch: true,
              workspaceId: workspaceId,
              websiteId,
              workonaTabId,
              confidence: 1.0, // 最高置信度
              matchType: 'exact'
            };
          } else {
            // 工作区不存在，清理无效映射
            await WorkonaTabManager.removeTabMapping(workonaTabId);
            console.log(`🗑️ 清理无效的 Workona ID 映射: ${workonaTabId}`);
          }
        }
      }

      // 移除URL匹配逻辑，只使用纯ID映射机制
      console.log(`🔍 标签页 ${tab.url} 没有Workona ID映射，不属于任何工作区`);

      return {
        isMatch: false,
        confidence: 0,
        matchType: 'none'
      };
    } catch (error) {
      console.error('检查工作区标签页匹配失败:', error);
      return {
        isMatch: false,
        confidence: 0,
        matchType: 'none'
      };
    }
  }

  // URL匹配方法已移除，使用纯ID映射机制



  // getAllWorkspaceWebsitesWithDetails 方法已移除，使用纯ID映射机制


}

/**
 * 用户标签页工具类
 * 提供用户标签页识别的通用方法
 */
export class UserTabsUtils {
  /**
   * 检查标签页是否为真正的用户标签页（非工作区管理的标签页）
   * 基于 isWorkspaceCore 元数据进行精确判断
   */
  static async isRealUserTab(tab: TabInfo): Promise<boolean> {
    try {
      console.log(`🔍 检查标签页: ${tab.title} (${tab.url}) - ID: ${tab.id}`);

      // 排除新标签页
      if (this.isNewTabPage(tab)) {
        console.log(`🚫 排除新标签页: ${tab.url} (${tab.title})`);
        return false;
      }

      // 基于 isWorkspaceCore 标记进行精确判断
      const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
      console.log(`🔍 Workona ID 查询结果:`, workonaIdResult);

      if (!workonaIdResult.success || !workonaIdResult.data) {
        // 没有 Workona ID 的标签页需要进一步判断
        // 使用新的3分类系统判断是否为普通网页（非系统页面）
        const { TabClassificationUtils } = await import('./workspaceSwitcher');
        if (tab.url && !TabClassificationUtils.isSystemTab(tab.url)) {
          console.log(`👤 识别为未分类的用户标签页（无 Workona ID 但为普通网页）: ${tab.url} (${tab.title})`);

          // 尝试为这个标签页创建 Workona ID 映射
          try {
            console.log(`🔄 尝试为未分类标签页创建 Workona ID: ${tab.title}`);
            const classifyResult = await TabManager.autoClassifyNewTab(tab.id, tab.url);
            if (classifyResult.success) {
              console.log(`✅ 成功为未分类标签页创建 Workona ID: ${tab.title}`);
              return true; // 现在它有了 Workona ID，是用户标签页
            } else {
              console.warn(`⚠️ 为未分类标签页创建 Workona ID 失败: ${tab.title}`, classifyResult.error);
              return true; // 即使创建失败，仍然认为是用户标签页
            }
          } catch (error) {
            console.warn(`⚠️ 自动分类标签页时出错: ${tab.title}`, error);
            return true; // 出错时仍然认为是用户标签页
          }
        } else {
          console.log(`🔧 识别为系统标签页（无 Workona ID 且为系统页面）: ${tab.url} (${tab.title})`);
          return false;
        }
      }

      const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);
      console.log(`🔍 元数据查询结果:`, metadataResult);

      if (!metadataResult.success || !metadataResult.data) {
        console.log(`⚠️ Workona ID 映射损坏: ${workonaIdResult.data}`);
        return false;
      }

      const { isWorkspaceCore } = metadataResult.data;

      if (isWorkspaceCore) {
        console.log(`🏢 识别为工作区核心标签页 (isWorkspaceCore: true): ${tab.title} (${tab.url})`);
        return false; // 工作区核心标签页不是"用户标签页"
      } else {
        console.log(`👤 识别为会话临时标签页 (isWorkspaceCore: false): ${tab.title} (${tab.url})`);
        return true; // 只有会话临时标签页才是"用户标签页"
      }
    } catch (error) {
      console.error(`❌ 检查用户标签页时出错:`, error);
      return false;
    }
  }

  /**
   * 检查是否为新标签页
   */
  private static isNewTabPage(tab: TabInfo): boolean {
    return tab.url === 'chrome://newtab/' || tab.url === 'chrome://new-tab-page/' ||
           tab.url === 'about:newtab' || tab.url === 'edge://newtab/' ||
           (tab.url && tab.url.startsWith('chrome://newtab')) ||
           (tab.title === 'New Tab' || tab.title === '新标签页' || tab.title === 'Neuer Tab');
  }




}



/**
 * 用户标签页实时状态监控器
 * 监控用户标签页的可见性变化并实时更新UI状态
 */
export class UserTabsRealTimeMonitor {
  private static isMonitoring = false;
  private static monitoringInterval: NodeJS.Timeout | null = null;
  private static lastStateSnapshot: Map<string, any> = new Map();
  private static readonly MONITOR_INTERVAL = 1000; // 1秒检查间隔

  /**
   * 启动实时监控
   */
  static startMonitoring(): void {
    if (this.isMonitoring) {
      console.log('📊 用户标签页实时监控已在运行中');
      return;
    }

    console.log('🚀 启动用户标签页实时监控');
    this.isMonitoring = true;

    // 立即执行一次检查
    this.checkUserTabsStateChanges();

    // 设置定时检查
    this.monitoringInterval = setInterval(() => {
      this.checkUserTabsStateChanges();
    }, this.MONITOR_INTERVAL);
  }

  /**
   * 停止实时监控
   */
  static stopMonitoring(): void {
    if (!this.isMonitoring) {
      return;
    }

    console.log('⏹️ 停止用户标签页实时监控');
    this.isMonitoring = false;

    if (this.monitoringInterval) {
      clearInterval(this.monitoringInterval);
      this.monitoringInterval = null;
    }

    this.lastStateSnapshot.clear();
  }

  /**
   * 检查用户标签页状态变化
   */
  private static async checkUserTabsStateChanges(): Promise<void> {
    try {
      // 获取当前活跃工作区（使用多种策略）
      const activeWorkspace = await this.getCurrentActiveWorkspace();

      if (!activeWorkspace) {
        return; // 没有活跃工作区，跳过检查
      }

      const workspaceId = activeWorkspace.id;

      // 获取当前工作区的用户标签页状态
      const currentState = await WorkspaceUserTabsVisibilityManager.getWorkspaceUserTabsState(workspaceId);

      if (!currentState.success) {
        return;
      }

      const stateData = currentState.data!;
      const stateKey = `workspace_${workspaceId}`;
      const lastState = this.lastStateSnapshot.get(stateKey);

      // 创建当前状态快照
      const currentSnapshot = {
        isHidden: stateData.isHidden,
        hiddenTabsCount: stateData.hiddenTabIds.length,
        totalUserTabs: stateData.totalUserTabs,
        visibleUserTabs: stateData.visibleUserTabs,
        actionType: stateData.actionType,
        timestamp: Date.now()
      };

      // 检查是否有状态变化
      if (lastState && this.hasStateChanged(lastState, currentSnapshot)) {
        console.log(`📊 检测到工作区 "${activeWorkspace.name}" 用户标签页状态变化:`, {
          前: lastState,
          后: currentSnapshot
        });

        // 发送状态变化事件
        this.notifyStateChange(workspaceId, currentSnapshot);
      }

      // 更新状态快照
      this.lastStateSnapshot.set(stateKey, currentSnapshot);

    } catch (error) {
      console.warn('检查用户标签页状态变化时出错:', error);
    }
  }

  /**
   * 获取当前活跃工作区（使用多种策略）
   */
  private static async getCurrentActiveWorkspace(): Promise<WorkSpace | null> {
    try {
      // 策略1：通过 WorkspaceSwitcher 检测
      const { WorkspaceSwitcher } = await import('./workspaceSwitcher');
      const activeWorkspaceResult = await WorkspaceSwitcher.detectActiveWorkspace();

      if (activeWorkspaceResult.success && activeWorkspaceResult.data) {
        console.log(`🎯 通过 WorkspaceSwitcher 检测到活跃工作区: ${activeWorkspaceResult.data.name}`);
        return activeWorkspaceResult.data;
      }

      // 策略2：通过存储的活跃工作区ID
      const currentWorkspaceResult = await WorkspaceSwitcher.getCurrentWorkspace();
      if (currentWorkspaceResult.success && currentWorkspaceResult.data) {
        console.log(`🎯 通过存储检测到活跃工作区: ${currentWorkspaceResult.data.name}`);
        return currentWorkspaceResult.data;
      }

      // 策略3：通过窗口中的标签页推断
      const currentWindow = await chrome.windows.getCurrent();
      const windowTabs = await chrome.tabs.query({ windowId: currentWindow.id });

      const workspaceTabCounts = new Map<string, number>();

      for (const tab of windowTabs) {
        if (tab.id) {
          const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
          if (workonaIdResult.success && workonaIdResult.data) {
            const workspaceId = workonaIdResult.data.split('-')[1];
            workspaceTabCounts.set(workspaceId, (workspaceTabCounts.get(workspaceId) || 0) + 1);
          }
        }
      }

      if (workspaceTabCounts.size > 0) {
        // 找到标签页最多的工作区
        const [mostActiveWorkspaceId] = [...workspaceTabCounts.entries()].reduce((a, b) => a[1] > b[1] ? a : b);

        const workspacesResult = await StorageManager.getWorkspaces();
        if (workspacesResult.success && workspacesResult.data) {
          const workspace = workspacesResult.data.find(w => w.id === mostActiveWorkspaceId);
          if (workspace) {
            console.log(`🎯 通过窗口标签页推断活跃工作区: ${workspace.name}`);
            return workspace;
          }
        }
      }

      console.log(`⚠️ 无法检测到当前活跃工作区`);
      return null;
    } catch (error) {
      console.warn('获取当前活跃工作区失败:', error);
      return null;
    }
  }

  /**
   * 检查状态是否发生变化
   */
  private static hasStateChanged(lastState: any, currentState: any): boolean {
    return (
      lastState.isHidden !== currentState.isHidden ||
      lastState.hiddenTabsCount !== currentState.hiddenTabsCount ||
      lastState.totalUserTabs !== currentState.totalUserTabs ||
      lastState.visibleUserTabs !== currentState.visibleUserTabs ||
      lastState.actionType !== currentState.actionType
    );
  }

  /**
   * 通知状态变化
   */
  private static async notifyStateChange(workspaceId: string, _newState: any): Promise<void> {
    try {
      // 使用工作区状态同步工具发送事件
      const { WorkspaceStateSync } = await import('./workspaceStateSync');
      WorkspaceStateSync.sendWorkspaceStateUpdate(workspaceId, 'userTabsVisibility');

      console.log(`📢 已发送用户标签页状态变化通知: 工作区 ${workspaceId}`);
    } catch (error) {
      console.warn('发送用户标签页状态变化通知失败:', error);
    }
  }

  /**
   * 强制刷新指定工作区的状态
   */
  static async forceRefreshWorkspaceState(workspaceId: string): Promise<void> {
    try {
      const stateKey = `workspace_${workspaceId}`;

      // 获取最新状态
      const currentState = await WorkspaceUserTabsVisibilityManager.getWorkspaceUserTabsState(workspaceId);

      if (currentState.success) {
        const stateData = currentState.data!;
        const newSnapshot = {
          isHidden: stateData.isHidden,
          hiddenTabsCount: stateData.hiddenTabIds.length,
          totalUserTabs: stateData.totalUserTabs,
          visibleUserTabs: stateData.visibleUserTabs,
          actionType: stateData.actionType,
          timestamp: Date.now()
        };

        // 更新快照并发送通知
        this.lastStateSnapshot.set(stateKey, newSnapshot);
        await this.notifyStateChange(workspaceId, newSnapshot);

        console.log(`🔄 强制刷新工作区 ${workspaceId} 用户标签页状态完成`);
      }
    } catch (error) {
      console.warn(`强制刷新工作区 ${workspaceId} 状态失败:`, error);
    }
  }

  /**
   * 获取监控状态
   */
  static getMonitoringStatus(): { isMonitoring: boolean; workspaceCount: number } {
    return {
      isMonitoring: this.isMonitoring,
      workspaceCount: this.lastStateSnapshot.size
    };
  }
}

/**
 * 工作区级别的用户标签页可见性管理器
 * 管理特定工作区中的用户标签页隐藏和显示
 */
export class WorkspaceUserTabsVisibilityManager {
  /**
   * 获取工作区用户标签页状态
   */
  static async getWorkspaceUserTabsState(workspaceId: string): Promise<OperationResult<{
    isHidden: boolean;
    hiddenTabIds: number[];
    pinnedTabIds: number[];
    totalUserTabs: number;
    visibleUserTabs: number;
    canContinueHiding: boolean;
    actionType: 'hide' | 'continue_hide' | 'show';
  }>> {
    try {
      // 从存储中获取工作区隐藏状态
      const result = await chrome.storage.local.get([
        `workspaceUserTabsHidden_${workspaceId}`,
        `workspaceHiddenTabIds_${workspaceId}`,
        `workspacePinnedTabIds_${workspaceId}`
      ]);
      const hiddenTabIds = result[`workspaceHiddenTabIds_${workspaceId}`] || [];
      const pinnedTabIds = result[`workspacePinnedTabIds_${workspaceId}`] || [];

      // 验证隐藏的标签页是否仍然存在
      const validHiddenTabIds: number[] = [];
      for (const tabId of hiddenTabIds) {
        try {
          await chrome.tabs.get(tabId);
          validHiddenTabIds.push(tabId);
        } catch {
          // 标签页已不存在，忽略
        }
      }

      // 如果有无效的隐藏标签页，更新存储
      if (validHiddenTabIds.length !== hiddenTabIds.length) {
        await chrome.storage.local.set({
          [`workspaceHiddenTabIds_${workspaceId}`]: validHiddenTabIds
        });

        // 如果没有有效的隐藏标签页，清除隐藏状态
        if (validHiddenTabIds.length === 0) {
          await chrome.storage.local.set({
            [`workspaceUserTabsHidden_${workspaceId}`]: false
          });
        }
      }

      // 获取当前窗口的所有标签页（使用 Chrome API 获取完整的 Tab 对象）
      const mainWindow = await chrome.windows.getCurrent();
      const allTabs = await chrome.tabs.query({ windowId: mainWindow.id });

      if (allTabs.length === 0) {
        return {
          success: true,
          data: {
            isHidden: false,
            hiddenTabIds: [],
            pinnedTabIds: [],
            totalUserTabs: 0,
            visibleUserTabs: 0,
            canContinueHiding: false,
            actionType: 'hide' as const,
          },
        };
      }

      // 计算工作区中的用户标签页
      const workspaceUserTabs = [];

      console.log(`🔍 开始分析工作区 ${workspaceId} 的用户标签页...`);

      for (const tab of allTabs) {
        if (!tab.id) continue;

        // 检查是否是用户标签页（转换为 TabInfo 格式）
        const tabInfo: TabInfo = {
          id: tab.id,
          url: tab.url || '',
          title: tab.title || '',
          favicon: tab.favIconUrl || '',
          isPinned: tab.pinned,
          isActive: tab.active,
          windowId: tab.windowId,
          index: tab.index
        };
        const isUserTab = await UserTabsUtils.isRealUserTab(tabInfo);
        if (!isUserTab) {
          continue;
        }

        // 检查是否属于当前工作区
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (workonaIdResult.success && workonaIdResult.data) {
          const workspaceIdFromMapping = workonaIdResult.data.split('-')[1];
          if (workspaceIdFromMapping === workspaceId) {
            workspaceUserTabs.push(tab);
            console.log(`✅ 找到工作区用户标签页: ${tab.title} (${tab.url})`);
          }
        } else {
          // 对于没有 Workona ID 的标签页，需要智能判断是否属于当前工作区
          console.log(`⚠️ 标签页没有 Workona ID，进行智能归属判断: ${tab.title} (${tab.url})`);

          // 检查是否应该归属到当前工作区
          const shouldBelongToWorkspace = await this.shouldUnmappedTabBelongToWorkspace(tab, workspaceId);

          if (shouldBelongToWorkspace) {
            workspaceUserTabs.push(tab);
            console.log(`✅ 将未分类用户标签页归属到工作区: ${tab.title} (${tab.url})`);

            // 尝试为这个标签页创建 Workona ID 映射
            try {
              const { TabManager } = await import('./tabs');
              const classifyResult = await TabManager.autoClassifyNewTab(tab.id, tab.url || '');
              if (classifyResult.success) {
                console.log(`🔗 成功为未分类标签页创建 Workona ID: ${tab.title}`);
              }
            } catch (error) {
              console.warn(`创建 Workona ID 映射失败: ${tab.title}`, error);
            }
          }
        }
      }

      // 计算可见的用户标签页（在主窗口中的）
      const currentWindow = await chrome.windows.getCurrent();
      const visibleUserTabs = workspaceUserTabs.filter(tab =>
        tab.windowId === currentWindow.id &&
        tab.id && !validHiddenTabIds.includes(tab.id)
      );

      // 总用户标签页数量 = 可见的 + 隐藏的
      const totalUserTabs = visibleUserTabs.length + validHiddenTabIds.length;

      // 判断操作类型
      const hasHiddenTabs = validHiddenTabIds.length > 0;
      const hasVisibleTabs = visibleUserTabs.length > 0;

      let actionType: 'hide' | 'continue_hide' | 'show';
      let canContinueHiding = false;

      if (hasHiddenTabs && hasVisibleTabs) {
        // 既有隐藏的又有可见的 -> 继续隐藏
        actionType = 'continue_hide';
        canContinueHiding = true;
      } else if (hasHiddenTabs && !hasVisibleTabs) {
        // 只有隐藏的，没有可见的 -> 显示
        actionType = 'show';
        canContinueHiding = false;
      } else {
        // 没有隐藏的，只有可见的 -> 隐藏
        actionType = 'hide';
        canContinueHiding = false;
      }

      console.log(`📊 工作区 ${workspaceId} 用户标签页状态: 总计 ${totalUserTabs} 个，可见 ${visibleUserTabs.length} 个，隐藏 ${validHiddenTabIds.length} 个`);

      return {
        success: true,
        data: {
          isHidden: hasHiddenTabs,
          hiddenTabIds: validHiddenTabIds,
          pinnedTabIds,
          totalUserTabs,
          visibleUserTabs: visibleUserTabs.length,
          canContinueHiding,
          actionType,
        },
      };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get workspace user tabs state',
          details: error,
        },
      };
    }
  }

  /**
   * 判断未映射的标签页是否应该归属到指定工作区
   */
  private static async shouldUnmappedTabBelongToWorkspace(_tab: chrome.tabs.Tab, workspaceId: string): Promise<boolean> {
    try {
      // 精确检查：使用存储中的活跃工作区ID（最准确的数据源）
      const activeIdResult = await StorageManager.getActiveWorkspaceId();
      if (activeIdResult.success && activeIdResult.data === workspaceId) {
        console.log(`🎯 存储中的活跃工作区精确匹配，归属标签页到工作区 ${workspaceId}`);
        return true;
      }

      // 备用检查：通过 WorkspaceSwitcher 检测的活跃工作区
      const { WorkspaceSwitcher } = await import('./workspaceSwitcher');
      const activeWorkspaceResult = await WorkspaceSwitcher.detectActiveWorkspace();

      // 如果当前活跃工作区就是目标工作区，则归属到该工作区
      if (activeWorkspaceResult.success && activeWorkspaceResult.data && activeWorkspaceResult.data.id === workspaceId) {
        console.log(`🎯 检测到的活跃工作区匹配，归属标签页到工作区 ${workspaceId}`);
        return true;
      }

      // 检查当前窗口是否主要属于该工作区
      const currentWindow = await chrome.windows.getCurrent();
      const windowTabs = await chrome.tabs.query({ windowId: currentWindow.id });

      // 统计窗口中属于该工作区的标签页数量
      let workspaceTabsCount = 0;
      let totalMappedTabs = 0;

      for (const windowTab of windowTabs) {
        if (windowTab.id) {
          const windowTabWorkonaId = await WorkonaTabManager.getWorkonaIdByChromeId(windowTab.id);
          if (windowTabWorkonaId.success && windowTabWorkonaId.data) {
            totalMappedTabs++;
            const windowTabWorkspaceId = windowTabWorkonaId.data.split('-')[1];
            if (windowTabWorkspaceId === workspaceId) {
              workspaceTabsCount++;
            }
          }
        }
      }

      console.log(`📊 窗口标签页归属分析: 工作区${workspaceId}有${workspaceTabsCount}个标签页，总映射标签页${totalMappedTabs}个`);

      // 如果窗口中有该工作区的标签页，且该工作区是主要工作区，则归属
      if (workspaceTabsCount > 0 && workspaceTabsCount >= totalMappedTabs / 2) {
        console.log(`✅ 窗口主要属于工作区 ${workspaceId}，归属未映射标签页`);
        return true;
      }

      // 如果窗口中没有任何映射的标签页，检查是否是默认工作区
      if (totalMappedTabs === 0) {
        const workspacesResult = await StorageManager.getWorkspaces();
        if (workspacesResult.success && workspacesResult.data && workspacesResult.data.length > 0) {
          const firstWorkspace = workspacesResult.data[0];
          if (firstWorkspace.id === workspaceId) {
            console.log(`🏠 使用默认工作区 ${workspaceId} 归属未映射标签页`);
            return true;
          }
        }
      }

      console.log(`❌ 标签页不应归属到工作区 ${workspaceId}`);
      return false;
    } catch (error) {
      console.warn('判断标签页归属时出错:', error);
      return false;
    }
  }

  /**
   * 设置工作区用户标签页隐藏状态
   */
  static async setWorkspaceUserTabsState(workspaceId: string, isHidden: boolean, hiddenTabIds: number[], pinnedTabIds?: number[]): Promise<OperationResult<void>> {
    try {
      const stateData: Record<string, any> = {
        [`workspaceUserTabsHidden_${workspaceId}`]: isHidden,
        [`workspaceHiddenTabIds_${workspaceId}`]: hiddenTabIds,
      };

      // 如果提供了固定标签页ID列表，也保存它
      if (pinnedTabIds !== undefined) {
        stateData[`workspacePinnedTabIds_${workspaceId}`] = pinnedTabIds;
      }

      await chrome.storage.local.set(stateData);
      return { success: true };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to set workspace user tabs state',
          details: error,
        },
      };
    }
  }

  /**
   * 切换工作区用户标签页的显示/隐藏状态
   */
  static async toggleWorkspaceUserTabsVisibility(workspace: WorkSpace): Promise<OperationResult<{
    action: 'hidden' | 'shown';
    tabIds: number[];
  }>> {
    try {
      console.log(`🔄 切换工作区 "${workspace.name}" 用户标签页的显示状态`);

      // 获取当前状态
      const stateResult = await this.getWorkspaceUserTabsState(workspace.id);
      if (!stateResult.success) {
        return {
          success: false,
          error: stateResult.error,
        };
      }

      const { actionType } = stateResult.data!;

      switch (actionType) {
        case 'show':
          // 显示所有隐藏的标签页
          const showResult = await this.showWorkspaceUserTabs(workspace.id);
          if (!showResult.success) {
            return {
              success: false,
              error: showResult.error,
            };
          }

          console.log(`✅ 工作区用户标签页显示成功，影响 ${showResult.data!.length} 个标签页`);
          return {
            success: true,
            data: {
              action: 'shown',
              tabIds: showResult.data!,
            },
          };

        case 'continue_hide':
          // 继续隐藏可见的标签页
          const continueHideResult = await this.continueHideWorkspaceUserTabs(workspace.id);
          if (!continueHideResult.success) {
            return {
              success: false,
              error: continueHideResult.error,
            };
          }

          console.log(`✅ 工作区用户标签页继续隐藏成功，影响 ${continueHideResult.data!.length} 个标签页`);
          return {
            success: true,
            data: {
              action: 'hidden',
              tabIds: continueHideResult.data!,
            },
          };

        case 'hide':
        default:
          // 隐藏所有可见的标签页
          const hideResult = await this.hideWorkspaceUserTabs(workspace.id);
          if (!hideResult.success) {
            return {
              success: false,
              error: hideResult.error,
            };
          }

          console.log(`✅ 工作区用户标签页隐藏成功，影响 ${hideResult.data!.length} 个标签页`);
          return {
            success: true,
            data: {
              action: 'hidden',
              tabIds: hideResult.data!,
            },
          };
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to toggle workspace user tabs visibility',
          details: error,
        },
      };
    }
  }

  /**
   * 隐藏工作区的用户标签页
   */
  private static async hideWorkspaceUserTabs(workspaceId: string): Promise<OperationResult<number[]>> {
    try {
      console.log(`🔒 开始隐藏工作区 ${workspaceId} 的用户标签页`);

      // 获取当前状态
      const stateResult = await this.getWorkspaceUserTabsState(workspaceId);
      if (!stateResult.success) {
        return {
          success: false,
          error: stateResult.error,
        };
      }

      const currentState = stateResult.data!;

      // 获取所有标签页
      const allTabsResult = await TabManager.getAllTabs();
      if (!allTabsResult.success) {
        return {
          success: false,
          error: allTabsResult.error,
        };
      }

      const allTabs = allTabsResult.data!;
      const currentWindow = await chrome.windows.getCurrent();

      // 筛选出当前窗口中属于该工作区的用户标签页
      const workspaceUserTabs = [];

      for (const tab of allTabs) {
        if (tab.windowId !== currentWindow.id) {
          continue;
        }

        // 检查是否是用户标签页
        const isUserTab = await UserTabsUtils.isRealUserTab(tab);
        if (!isUserTab) {
          continue;
        }

        // 检查是否属于当前工作区
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (workonaIdResult.success && workonaIdResult.data) {
          const workspaceIdFromMapping = workonaIdResult.data.split('-')[1];
          if (workspaceIdFromMapping === workspaceId && !currentState.hiddenTabIds.includes(tab.id)) {
            workspaceUserTabs.push(tab);
          }
        } else {
          // 对于没有 Workona ID 的用户标签页，也应该被包含在隐藏操作中
          if (!currentState.hiddenTabIds.includes(tab.id)) {
            workspaceUserTabs.push(tab);
            console.log(`✅ 将未分类用户标签页包含在隐藏操作中: ${tab.title} (${tab.url})`);
          }
        }
      }

      if (workspaceUserTabs.length === 0) {
        console.log(`⚠️ 工作区 ${workspaceId} 没有用户标签页需要隐藏`);
        return { success: true, data: [] };
      }

      console.log(`📤 准备隐藏工作区 ${workspaceId} 的 ${workspaceUserTabs.length} 个用户标签页`);

      // 记录哪些标签页是固定的（基于 Workona ID 映射的元数据）
      const pinnedTabIds: number[] = [];
      for (const tab of workspaceUserTabs) {
        // 检查标签页的实际固定状态和元数据中的固定状态
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        let shouldRecordAsPinned = tab.isPinned;

        if (workonaIdResult.success && workonaIdResult.data) {
          // 如果有 Workona ID，检查元数据中的固定状态
          const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);
          if (metadataResult.success && metadataResult.data?.metadata?.isPinned !== undefined) {
            // 使用元数据中的固定状态作为权威状态
            shouldRecordAsPinned = metadataResult.data.metadata.isPinned;
          }
        }

        if (shouldRecordAsPinned) {
          pinnedTabIds.push(tab.id);
          console.log(`📌 记录固定标签页: ${tab.title} (${tab.id}) - 基于${workonaIdResult.success ? 'Workona元数据' : '实际状态'}`);
        }
      }

      // 移动用户标签页到工作区专用窗口
      const tabIds = workspaceUserTabs.map(tab => tab.id).filter(id => id && typeof id === 'number' && id > 0);

      if (tabIds.length === 0) {
        console.warn(`⚠️ 工作区 ${workspaceId} 没有有效的标签页ID可以隐藏`);
        return { success: true, data: [] };
      }

      const moveResult = await WindowManager.moveTabsToWorkspaceWindow(
        tabIds,
        `workspace-${workspaceId}-hidden-tabs`,
        `工作区 ${workspaceId} - 隐藏的用户标签页`
      );

      if (!moveResult.success) {
        console.error(`❌ 移动工作区 ${workspaceId} 用户标签页到隐藏窗口失败:`, moveResult.error);
        return {
          success: false,
          error: moveResult.error,
        };
      }

      // 更新工作区隐藏状态，包括固定状态
      const newHiddenTabIds = [...currentState.hiddenTabIds, ...tabIds];
      const existingPinnedTabIds = currentState.pinnedTabIds || [];
      const allPinnedTabIds = [...existingPinnedTabIds, ...pinnedTabIds];
      await this.setWorkspaceUserTabsState(workspaceId, true, newHiddenTabIds, allPinnedTabIds);

      console.log(`✅ 成功隐藏工作区 ${workspaceId} 的 ${tabIds.length} 个用户标签页`);
      return { success: true, data: tabIds };
    } catch (error) {
      console.error(`❌ 隐藏工作区 ${workspaceId} 用户标签页失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to hide workspace user tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 继续隐藏工作区的可见用户标签页
   */
  private static async continueHideWorkspaceUserTabs(workspaceId: string): Promise<OperationResult<number[]>> {
    try {
      console.log(`🔒 继续隐藏工作区 ${workspaceId} 的可见用户标签页`);

      // 获取当前状态
      const stateResult = await this.getWorkspaceUserTabsState(workspaceId);
      if (!stateResult.success) {
        return {
          success: false,
          error: stateResult.error,
        };
      }

      const currentState = stateResult.data!;

      // 获取所有标签页
      const allTabsResult = await TabManager.getAllTabs();
      if (!allTabsResult.success) {
        return {
          success: false,
          error: allTabsResult.error,
        };
      }

      const allTabs = allTabsResult.data!;
      const currentWindow = await chrome.windows.getCurrent();

      // 筛选出当前窗口中属于该工作区的可见用户标签页
      const visibleWorkspaceUserTabs = [];

      for (const tab of allTabs) {
        if (tab.windowId !== currentWindow.id || currentState.hiddenTabIds.includes(tab.id)) {
          continue;
        }

        // 检查是否是用户标签页
        const isUserTab = await UserTabsUtils.isRealUserTab(tab);
        if (!isUserTab) {
          continue;
        }

        // 检查是否属于当前工作区
        const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);
        if (workonaIdResult.success && workonaIdResult.data) {
          const workspaceIdFromMapping = workonaIdResult.data.split('-')[1];
          if (workspaceIdFromMapping === workspaceId) {
            visibleWorkspaceUserTabs.push(tab);
          }
        } else {
          // 对于没有 Workona ID 的用户标签页，也应该被包含在继续隐藏操作中
          visibleWorkspaceUserTabs.push(tab);
          console.log(`✅ 将未分类用户标签页包含在继续隐藏操作中: ${tab.title} (${tab.url})`);
        }
      }

      if (visibleWorkspaceUserTabs.length === 0) {
        console.log(`⚠️ 工作区 ${workspaceId} 没有新的可见用户标签页需要隐藏`);
        return { success: true, data: [] };
      }

      console.log(`📤 准备继续隐藏工作区 ${workspaceId} 的 ${visibleWorkspaceUserTabs.length} 个可见用户标签页`);

      // 移动新的可见标签页到隐藏窗口
      const newTabIds = visibleWorkspaceUserTabs.map(tab => tab.id).filter(id => id && typeof id === 'number' && id > 0);

      if (newTabIds.length === 0) {
        console.warn(`⚠️ 工作区 ${workspaceId} 没有有效的标签页ID可以继续隐藏`);
        return { success: true, data: [] };
      }

      const moveResult = await WindowManager.moveTabsToWorkspaceWindow(
        newTabIds,
        `workspace-${workspaceId}-hidden-tabs`,
        `工作区 ${workspaceId} - 隐藏的用户标签页`
      );

      if (!moveResult.success) {
        console.error(`❌ 移动工作区 ${workspaceId} 用户标签页到隐藏窗口失败:`, moveResult.error);
        return {
          success: false,
          error: moveResult.error,
        };
      }

      // 更新隐藏状态，合并新的标签页ID
      const allHiddenTabIds = [...currentState.hiddenTabIds, ...newTabIds];
      await this.setWorkspaceUserTabsState(workspaceId, true, allHiddenTabIds);

      console.log(`✅ 成功继续隐藏工作区 ${workspaceId} 的 ${newTabIds.length} 个用户标签页`);
      return { success: true, data: newTabIds };
    } catch (error) {
      console.error(`❌ 继续隐藏工作区 ${workspaceId} 用户标签页失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to continue hiding workspace user tabs',
          details: error,
        },
      };
    }
  }

  /**
   * 显示工作区的隐藏用户标签页
   */
  private static async showWorkspaceUserTabs(workspaceId: string): Promise<OperationResult<number[]>> {
    try {
      console.log(`🔓 开始显示工作区 ${workspaceId} 的隐藏用户标签页`);

      // 获取隐藏状态
      const stateResult = await this.getWorkspaceUserTabsState(workspaceId);
      if (!stateResult.success) {
        return {
          success: false,
          error: stateResult.error,
        };
      }

      const { isHidden, hiddenTabIds, pinnedTabIds } = stateResult.data!;

      if (!isHidden || hiddenTabIds.length === 0) {
        console.log(`⚠️ 工作区 ${workspaceId} 没有隐藏的用户标签页需要显示`);
        return { success: true, data: [] };
      }

      console.log(`📥 准备显示工作区 ${workspaceId} 的 ${hiddenTabIds.length} 个隐藏用户标签页`);

      // 验证隐藏的标签页是否仍然存在
      const existingTabIds: number[] = [];
      for (const tabId of hiddenTabIds) {
        try {
          await chrome.tabs.get(tabId);
          existingTabIds.push(tabId);
        } catch {
          console.log(`⚠️ 工作区 ${workspaceId} 的标签页 ${tabId} 已不存在，将从隐藏列表中移除`);
        }
      }

      if (existingTabIds.length === 0) {
        console.log(`⚠️ 工作区 ${workspaceId} 所有隐藏的标签页都已不存在`);
        await this.setWorkspaceUserTabsState(workspaceId, false, []);
        return { success: true, data: [] };
      }

      // 获取当前窗口ID
      const currentWindow = await chrome.windows.getCurrent();

      // 直接使用 Chrome API 移动标签页回到主窗口
      try {
        console.log(`📥 移动 ${existingTabIds.length} 个隐藏标签页回到主窗口 ${currentWindow.id}`);

        await chrome.tabs.move(existingTabIds, {
          windowId: currentWindow.id!,
          index: -1 // 移动到窗口末尾
        });

        // 恢复固定状态（基于 Workona ID 映射的元数据）
        for (const tabId of existingTabIds) {
          try {
            // 检查标签页的 Workona ID 和元数据
            const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
            let shouldRestorePinned = pinnedTabIds.includes(tabId);

            if (workonaIdResult.success && workonaIdResult.data) {
              // 如果有 Workona ID，检查元数据中的固定状态
              const metadataResult = await WorkonaTabManager.getTabMetadata(workonaIdResult.data);
              if (metadataResult.success && metadataResult.data?.metadata?.isPinned !== undefined) {
                // 使用元数据中的固定状态作为权威状态
                shouldRestorePinned = metadataResult.data.metadata.isPinned;
              }
            }

            if (shouldRestorePinned) {
              await chrome.tabs.update(tabId, { pinned: true });
              console.log(`📌 恢复固定状态: 标签页 ${tabId} - 基于${workonaIdResult.success ? 'Workona元数据' : '存储记录'}`);
            } else {
              // 确保非固定标签页不会被错误地设置为固定
              const currentTab = await chrome.tabs.get(tabId);
              if (currentTab.pinned) {
                await chrome.tabs.update(tabId, { pinned: false });
                console.log(`📌 取消错误的固定状态: 标签页 ${tabId}`);
              }
            }
          } catch (error) {
            console.warn(`⚠️ 处理标签页 ${tabId} 固定状态失败:`, error);
          }
        }

        // 清除隐藏状态
        await this.setWorkspaceUserTabsState(workspaceId, false, []);

        console.log(`✅ 成功显示工作区 ${workspaceId} 的 ${existingTabIds.length} 个隐藏用户标签页`);
        return { success: true, data: existingTabIds };
      } catch (moveError) {
        console.error(`❌ 移动工作区 ${workspaceId} 隐藏标签页回主窗口失败:`, moveError);
        return {
          success: false,
          error: {
            code: ERROR_CODES.TAB_ERROR,
            message: 'Failed to move tabs back to main window',
            details: moveError,
          },
        };
      }
    } catch (error) {
      console.error(`❌ 显示工作区 ${workspaceId} 用户标签页失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to show workspace user tabs',
          details: error,
        },
      };
    }
  }
}

// 类已在上面单独导出
