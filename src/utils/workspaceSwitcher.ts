import {
  WorkSpace,
  WorkspaceSwitchOptions,
  OperationResult,
  TabInfo
} from '@/types/workspace';
import { StorageManager } from './storage';
import { TabManager } from './tabs';
import { WorkspaceManager } from './workspace';
import { WindowManager } from './windowManager';
import { WorkspaceSessionManager } from './workspaceSessionManager';
import { WorkonaTabManager } from './workonaTabManager';
// Workona 风格：移除传统 URL 匹配依赖
import { ERROR_CODES } from './constants';

/**
 * 新的3分类标签页系统工具类
 *
 * 分类定义：
 * 1. 系统标签页：Chrome内置页面（chrome://、chrome-extension://、about:等）
 * 2. 工作区专属标签页：具有Workona ID映射的标签页
 * 3. 用户标签页：普通网站标签页，不具有Workona ID映射
 */
export class TabClassificationUtils {
  /**
   * 判断是否为系统标签页
   */
  static isSystemTab(url: string): boolean {
    return url.includes('chrome://') ||
           url.includes('chrome-extension://') ||
           url.includes('about:') ||
           url.includes('edge://') ||
           url.includes('workspace-placeholder.html') ||
           url === 'chrome://newtab/' ||
           url === 'about:blank' ||
           url === '';
  }

  /**
   * 检查是否为工作区专属标签页（具有Workona ID映射）
   */
  static async isWorkspaceSpecificTab(tabId: number): Promise<boolean> {
    try {
      const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tabId);
      return workonaIdResult.success && !!workonaIdResult.data;
    } catch {
      return false;
    }
  }

  /**
   * 检查是否为用户标签页（普通网站标签页，不具有Workona ID映射）
   */
  static async isUserTab(tab: { id: number; url?: string }): Promise<boolean> {
    if (!tab.url || this.isSystemTab(tab.url)) {
      return false;
    }

    const isWorkspaceSpecific = await this.isWorkspaceSpecificTab(tab.id);
    return !isWorkspaceSpecific;
  }

  /**
   * 对标签页进行3分类统计
   */
  static async classifyTabs(tabs: chrome.tabs.Tab[]): Promise<{
    systemTabs: chrome.tabs.Tab[];
    workspaceSpecificTabs: chrome.tabs.Tab[];
    userTabs: chrome.tabs.Tab[];
  }> {
    const systemTabs: chrome.tabs.Tab[] = [];
    const workspaceSpecificTabs: chrome.tabs.Tab[] = [];
    const userTabs: chrome.tabs.Tab[] = [];

    for (const tab of tabs) {
      if (!tab.url || !tab.id) continue;

      if (this.isSystemTab(tab.url)) {
        systemTabs.push(tab);
      } else {
        const isWorkspaceSpecific = await this.isWorkspaceSpecificTab(tab.id);
        if (isWorkspaceSpecific) {
          workspaceSpecificTabs.push(tab);
        } else {
          userTabs.push(tab);
        }
      }
    }

    return { systemTabs, workspaceSpecificTabs, userTabs };
  }
}

/**
 * 工作区切换管理类
 */
export class WorkspaceSwitcher {


  /**
   * 切换到指定工作区（专用窗口架构）
   */
  static async switchToWorkspace(
    workspaceId: string,
    options: WorkspaceSwitchOptions = {}
  ): Promise<OperationResult<void>> {
    try {
      console.log(`开始切换到工作区: ${workspaceId}`);

      // 获取目标工作区
      const workspaceResult = await StorageManager.getWorkspace(workspaceId);
      if (!workspaceResult.success) {
        return { success: false, error: workspaceResult.error };
      }

      const workspace = workspaceResult.data!;

      // 获取当前活跃工作区
      const currentWorkspaceResult = await this.getCurrentWorkspace();
      const currentWorkspace = currentWorkspaceResult.success ? currentWorkspaceResult.data : null;

      // 获取设置
      const settingsResult = await StorageManager.getSettings();
      if (!settingsResult.success) {
        return { success: false, error: settingsResult.error };
      }

      const settings = settingsResult.data!;

      // 合并选项和设置
      const switchOptions: WorkspaceSwitchOptions = {
        closeOtherTabs: options.closeOtherTabs ?? settings.autoCloseOtherTabs,
        preserveUserOpenedTabs: options.preserveUserOpenedTabs ?? settings.preserveUserOpenedTabs,
        focusFirstTab: options.focusFirstTab ?? false, // 默认不自动聚焦到第一个标签页
      };

      // 0. 保存当前工作区状态（在切换前）
      if (currentWorkspace) {
        console.log(`💾 保存当前工作区状态: ${currentWorkspace.name}`);
        await WorkspaceSessionManager.syncCurrentWorkspaceState();
      }

      // 1. 重新分类临时标签页到当前工作区
      await this.reclassifyTemporaryTabs(workspaceId);

      // 1. 处理当前窗口中的标签页
      if (currentWorkspace && currentWorkspace.id !== workspaceId) {
        // 如果有当前工作区且不是目标工作区，将其标签页移动到专用窗口
        await this.moveCurrentTabsToWorkspaceWindow(currentWorkspace);
      } else if (!currentWorkspace) {
        // 如果没有当前工作区，检查是否有其他工作区的标签页需要移动
        await this.moveNonTargetWorkspaceTabsToWindow(workspaceId);
      }

      // 2. 从目标工作区的专用窗口移动标签页到主窗口
      await this.moveTabsFromWorkspaceWindow(workspace);

      // 3. 智能检查并打开工作区中缺失的网站
      await this.openWorkspaceWebsites(workspace);

      // 4. 处理用户标签页的隐藏状态（如果之前有隐藏的用户标签页）
      await this.handleUserTabsVisibilityState(workspace);

      // 5. 设置为活跃工作区
      await StorageManager.setActiveWorkspaceId(workspaceId);

      // 6. 更新工作区状态
      const workspacesResult = await StorageManager.getWorkspaces();
      if (workspacesResult.success) {
        const workspaces = workspacesResult.data!;
        workspaces.forEach(w => {
          w.isActive = w.id === workspaceId;
        });
        await StorageManager.saveWorkspaces(workspaces);
      }

      // 7. 执行会话恢复（在所有窗口操作完成后）
      console.log(`🔄 开始工作区会话恢复: ${workspaceId}`);
      const sessionSwitchResult = await WorkspaceSessionManager.switchSession(workspaceId, {
        preserveCurrentSession: true,
        restoreTabOrder: true, // 启用标签页顺序恢复
        activateFirstTab: false, // 不自动激活第一个标签页，而是恢复上次的活跃标签页
        closeOtherWorkspaceTabs: switchOptions.closeOtherTabs
      });

      if (!sessionSwitchResult.success) {
        console.warn('工作区会话恢复失败，但工作区切换成功:', sessionSwitchResult.error);
      } else {
        console.log('✅ 工作区会话恢复成功');
      }

      // 8. 发送工作区切换完成事件，通知UI更新状态
      await this.notifyWorkspaceSwitchComplete(workspaceId);

      console.log(`✅ 成功切换到工作区: ${workspace.name}`);
      return { success: true };
    } catch (error) {
      console.error(`切换工作区失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to switch workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 严格窗口保护机制
   * 基于新的3分类系统进行精确的窗口保护判断
   *
   * 新的3分类系统：
   * 1. 系统标签页：Chrome内置页面（chrome://、chrome-extension://、about:等）
   * 2. 工作区专属标签页：具有Workona ID映射的标签页
   * 3. 用户标签页：普通网站标签页，不具有Workona ID映射
   */
  private static async strictWindowProtection(
    allTabs: chrome.tabs.Tab[],
    tabsToMove: number[]
  ): Promise<void> {
    try {
      console.log('🛡️ [WorkspaceSwitcher] 执行严格窗口保护检查（基于3分类系统）...');

      // 📊 使用新的3分类系统进行标签页统计
      const classification = await TabClassificationUtils.classifyTabs(allTabs);
      const systemTabs = classification.systemTabs.length;
      const workspaceSpecificTabs = classification.workspaceSpecificTabs.length;
      const userTabs = classification.userTabs.length;

      // 计算移动后剩余的标签页总数和分类
      const totalRemainingTabsCount = allTabs.length - tabsToMove.length;

      console.log('📊 [WorkspaceSwitcher] 基于3分类系统的窗口状态分析:', {
        当前窗口总标签页: allTabs.length,
        系统标签页: systemTabs,
        工作区专属标签页: workspaceSpecificTabs,
        用户标签页: userTabs,
        要移动的标签页: tabsToMove.length,
        移动后剩余标签页总数: totalRemainingTabsCount
      });

      // 🚨 严格条件：只有当移动后窗口只剩下1个标签页时才创建保护标签页
      if (totalRemainingTabsCount === 1) {
        console.log('🚨 [WorkspaceSwitcher] 检测到严格保护条件：移动后窗口只剩1个标签页');

        // 检查剩余的那个标签页的详细信息
        const remainingTabs = allTabs.filter(tab => tab.id && !tabsToMove.includes(tab.id));

        if (remainingTabs.length === 1) {
          const remainingTab = remainingTabs[0];
          console.log(`🔍 [WorkspaceSwitcher] 剩余标签页详情: ${remainingTab.title} (${remainingTab.url})`);

          // 基于3分类系统判断是否需要保护
          if (remainingTab.url && TabClassificationUtils.isSystemTab(remainingTab.url)) {
            console.log('✅ [WorkspaceSwitcher] 剩余标签页是系统标签页，窗口已安全，无需额外保护');
          } else {
            // 创建保护标签页
            console.log('🆕 [WorkspaceSwitcher] 剩余标签页是用户/工作区标签页，创建系统保护标签页');

            const newTab = await chrome.tabs.create({
              url: 'chrome://newtab/',
              active: true,
            });

            if (newTab.id) {
              console.log(`✅ [WorkspaceSwitcher] 成功创建严格保护标签页: ${newTab.id}`);
            } else {
              console.error('❌ [WorkspaceSwitcher] 创建严格保护标签页失败：无法获取标签页ID');
            }
          }
        } else {
          console.log(`ℹ️ [WorkspaceSwitcher] 剩余标签页数量异常: ${remainingTabs.length}，跳过保护`);
        }
      } else if (totalRemainingTabsCount === 0) {
        console.log('🚨 [WorkspaceSwitcher] 检测到极端情况：移动后窗口将没有标签页');

        const newTab = await chrome.tabs.create({
          url: 'chrome://newtab/',
          active: true,
        });

        if (newTab.id) {
          console.log(`✅ [WorkspaceSwitcher] 成功创建紧急保护标签页: ${newTab.id}`);
        }
      } else {
        console.log(`✅ [WorkspaceSwitcher] 窗口安全：移动后将剩余 ${totalRemainingTabsCount} 个标签页，无需保护`);
      }
    } catch (error) {
      console.warn('[WorkspaceSwitcher] 严格窗口保护检查失败:', error);
      // 出错时创建一个保护性标签页
      try {
        await chrome.tabs.create({
          url: 'chrome://newtab/',
          active: false
        });
        console.log('🆘 [WorkspaceSwitcher] 安全检查失败，已创建紧急保护性标签页');
      } catch (emergencyError) {
        console.error('❌ [WorkspaceSwitcher] 紧急保护性标签页创建失败:', emergencyError);
      }
    }
  }





  /**
   * 移动非目标工作区的标签页到专用窗口
   */
  private static async moveNonTargetWorkspaceTabsToWindow(targetWorkspaceId: string): Promise<OperationResult<void>> {
    try {
      console.log(`检查并移动非目标工作区的标签页到专用窗口，目标工作区: ${targetWorkspaceId}`);

      // 获取所有工作区
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        console.log('获取工作区列表失败:', workspacesResult.error);
        return { success: true }; // 不阻断流程
      }

      const workspaces = workspacesResult.data!;

      // 获取当前窗口的所有标签页
      const currentWindow = await chrome.windows.getCurrent();
      const currentTabs = await chrome.tabs.query({ windowId: currentWindow.id });
      console.log(`当前窗口共有 ${currentTabs.length} 个标签页`);

      // 检查每个非目标工作区，看是否有真正管理的标签页需要移动
      for (const workspace of workspaces) {
        if (workspace.id === targetWorkspaceId) {
          continue; // 跳过目标工作区
        }

        // 使用智能识别逻辑，只移动工作区真正管理的标签页
        const relatedTabs: TabInfo[] = [];

        for (const tab of currentTabs) {
          if (!tab.url || !tab.id) continue;

          // 检查是否匹配工作区的网站配置
          const matchingWebsite = workspace.websites.find(website => {
            // Workona 风格：仅基于 URL 匹配，不再依赖固定状态
            return tab.url!.startsWith(website.url);
          });

          if (matchingWebsite) {
            relatedTabs.push({
              id: tab.id,
              url: tab.url,
              title: tab.title || '',
              favicon: tab.favIconUrl || '',
              isPinned: tab.pinned,
              isActive: tab.active,
              windowId: tab.windowId,
              index: tab.index
            });
          }
        }

        if (relatedTabs.length > 0) {
          console.log(`发现工作区 "${workspace.name}" 的 ${relatedTabs.length} 个真正管理的标签页需要移动到专用窗口`);

          const tabIds = relatedTabs.map(tab => tab.id);

          // 安全检查：确保移动后窗口不会关闭
          await this.strictWindowProtection(currentTabs, tabIds);

          const moveResult = await WindowManager.moveTabsToWorkspaceWindow(
            tabIds,
            workspace.id,
            workspace.name
          );

          if (moveResult.success) {
            console.log(`成功移动工作区 "${workspace.name}" 的 ${tabIds.length} 个标签页到专用窗口`);
          } else {
            console.error(`移动工作区 "${workspace.name}" 的标签页失败:`, moveResult.error);
          }
        }
      }

      return { success: true };
    } catch (error) {
      console.error('移动非目标工作区标签页失败:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to move non-target workspace tabs',
          details: error,
        },
      };
    }
  }



  /**
   * 将当前窗口的所有标签页移动到工作区专用窗口（Workona 风格：完整会话隔离）
   */
  private static async moveCurrentTabsToWorkspaceWindow(workspace: WorkSpace): Promise<OperationResult<void>> {
    try {
      console.log(`🔄 将当前窗口的所有标签页移动到工作区 ${workspace.name} 的专用窗口 (完整会话隔离)`);

      // 获取当前窗口的所有标签页
      const currentWindow = await chrome.windows.getCurrent();
      const allTabs = await chrome.tabs.query({ windowId: currentWindow.id });

      // 记录固定状态到 Workona ID 映射中
      await this.saveWorkspacePinnedStates(workspace, allTabs);

      // 筛选出需要移动的标签页（排除系统标签页）
      const tabsToMove: number[] = [];
      const systemTabsCount = allTabs.filter(tab =>
        tab.url && TabClassificationUtils.isSystemTab(tab.url)
      ).length;

      for (const tab of allTabs) {
        // 排除系统标签页
        if (tab.url && TabClassificationUtils.isSystemTab(tab.url)) {
          console.log(`🚫 跳过系统标签页: ${tab.url}`);
          continue;
        }

        // 包含所有非系统标签页（工作区核心标签页 + 会话临时标签页）
        if (tab.id) {
          tabsToMove.push(tab.id);
          console.log(`📦 准备移动标签页: ${tab.title} (${tab.url})`);
        }
      }

      console.log(`📊 当前窗口状态: 总标签页 ${allTabs.length} 个，系统标签页 ${systemTabsCount} 个，需移动 ${tabsToMove.length} 个`);

      if (tabsToMove.length === 0) {
        console.log(`ℹ️ 当前窗口没有需要移动的标签页`);
        return { success: true };
      }

      // 检查移动后是否需要保护性标签页
      const remainingTabsCount = allTabs.length - tabsToMove.length;
      console.log('📊 移动前状态分析:', {
        当前窗口总标签页: allTabs.length,
        系统标签页: systemTabsCount,
        要移动的标签页: tabsToMove.length,
        移动后剩余标签页: remainingTabsCount
      });

      // 安全检查：确保移动后窗口不会关闭
      await this.strictWindowProtection(allTabs, tabsToMove);

      // 移动标签页到专用窗口
      console.log(`🚀 准备移动 ${tabsToMove.length} 个标签页到专用窗口`);
      const moveResult = await WindowManager.moveTabsToWorkspaceWindow(
        tabsToMove,
        workspace.id,
        workspace.name
      );

      if (moveResult.success) {
        console.log(`✅ 成功移动 ${tabsToMove.length} 个标签页到工作区专用窗口`);

        // 移动完成后再次检查窗口状态
        const finalTabs = await chrome.tabs.query({ windowId: currentWindow.id });
        const finalSystemTabsCount = finalTabs.filter(tab =>
          tab.url && TabClassificationUtils.isSystemTab(tab.url)
        ).length;

        console.log('📊 移动后窗口状态:', {
          剩余标签页: finalTabs.length,
          系统标签页: finalSystemTabsCount,
          用户标签页: finalTabs.length - finalSystemTabsCount
        });
      } else {
        console.error(`❌ 移动标签页到专用窗口失败:`, moveResult.error);
      }

      return moveResult;
    } catch (error) {
      console.error(`移动当前标签页到工作区专用窗口失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to move current tabs to workspace window',
          details: error,
        },
      };
    }
  }

  /**
   * 从工作区专用窗口移动标签页到当前窗口（优化版）
   */
  private static async moveTabsFromWorkspaceWindow(workspace: WorkSpace): Promise<OperationResult<void>> {
    try {
      console.log(`📥 从工作区 ${workspace.name} 的专用窗口移动标签页到主窗口`);

      // 从专用窗口移动标签页
      const moveResult = await WindowManager.moveTabsFromWorkspaceWindow(workspace.id);

      if (moveResult.success) {
        const movedTabs = moveResult.data!;
        console.log(`✅ 成功从专用窗口移动 ${movedTabs.length} 个标签页到主窗口`);

        // 如果移动了标签页，添加短暂延迟确保标签页完全加载到主窗口
        if (movedTabs.length > 0) {
          console.log('⏳ 等待标签页完全加载到主窗口...');
          await new Promise(resolve => setTimeout(resolve, 50));

          // 恢复固定状态（基于 Workona ID 映射）
          await this.restoreWorkspacePinnedStates(workspace);

          // 清理旧的基于 Chrome ID 的固定状态存储
          await this.cleanupLegacyPinnedStates(workspace);
        }
      } else {
        console.error(`❌ 从专用窗口移动标签页失败:`, moveResult.error);
      }

      return { success: true }; // 即使失败也不阻断流程
    } catch (error) {
      console.error(`❌ 从工作区专用窗口移动标签页失败:`, error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to move tabs from workspace window',
          details: error,
        },
      };
    }
  }

  /**
   * 保存工作区的固定状态到 Workona ID 映射中
   */
  private static async saveWorkspacePinnedStates(workspace: WorkSpace, tabs: chrome.tabs.Tab[]): Promise<void> {
    try {
      console.log(`💾 保存工作区 "${workspace.name}" 的固定状态到 Workona ID 映射...`);

      let savedCount = 0;

      for (const tab of tabs) {
        if (tab.pinned && tab.id) {
          // 获取标签页的 Workona ID
          const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(tab.id);

          if (workonaIdResult.success && workonaIdResult.data) {
            const workonaId = workonaIdResult.data;

            // 获取现有的元数据
            const metadataResult = await WorkonaTabManager.getTabMetadata(workonaId);
            const existingMetadata = metadataResult.success ? metadataResult.data?.metadata : undefined;

            // 更新映射中的固定状态
            await WorkonaTabManager.updateTabMetadata(workonaId, {
              metadata: {
                ...existingMetadata,
                source: existingMetadata?.source || 'workspace_website',
                isPinned: true,
                pinnedAt: Date.now()
              }
            });

            console.log(`💾 保存固定状态: ${workonaId} -> Chrome ID ${tab.id} (${tab.title})`);
            savedCount++;
          } else {
            console.warn(`⚠️ 无法找到标签页 ${tab.id} 的 Workona ID，跳过固定状态保存`);
          }
        }
      }

      console.log(`✅ 工作区 "${workspace.name}" 固定状态保存完成: ${savedCount} 个标签页`);
    } catch (error) {
      console.error('保存工作区固定状态失败:', error);
    }
  }

  /**
   * 恢复工作区的固定状态（简化版：仅恢复用户主动设置的固定状态）
   */
  private static async restoreWorkspacePinnedStates(workspace: WorkSpace): Promise<void> {
    try {
      console.log(`📌 开始恢复工作区 "${workspace.name}" 的用户设置固定状态...`);

      // 获取所有标签页映射
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        console.warn('获取标签页映射失败:', mappingsResult.error);
        return;
      }

      const mappings = mappingsResult.data!;

      // 找到该工作区的所有映射，且元数据中标记为用户主动固定的
      const workspaceMappings = mappings.filter(mapping =>
        mapping.workspaceId === workspace.id &&
        mapping.chromeId !== null &&
        mapping.metadata?.isPinned === true &&
        mapping.metadata?.pinnedAt && // 必须有明确的固定时间戳
        mapping.metadata?.source !== 'session_restored' // 排除浏览器重启恢复的
      );

      if (workspaceMappings.length === 0) {
        console.log(`📌 工作区 "${workspace.name}" 没有用户主动设置的固定状态需要恢复`);
        return;
      }

      console.log(`📌 找到 ${workspaceMappings.length} 个用户主动设置的固定标签页`);

      let restoredCount = 0;
      let failedCount = 0;

      for (const mapping of workspaceMappings) {
        try {
          // 检查标签页是否仍然存在
          const tab = await chrome.tabs.get(mapping.chromeId!);

          if (tab && !tab.pinned) {
            // 恢复用户主动设置的固定状态
            await chrome.tabs.update(mapping.chromeId!, { pinned: true });
            console.log(`📌 恢复用户固定状态: ${mapping.workonaId} -> Chrome ID ${mapping.chromeId} (${tab.title})`);
            restoredCount++;
          } else if (tab && tab.pinned) {
            console.log(`✅ 标签页已经是固定状态: ${mapping.workonaId} -> Chrome ID ${mapping.chromeId}`);
            restoredCount++;
          }
        } catch (error) {
          console.warn(`⚠️ 恢复标签页 ${mapping.workonaId} (Chrome ID: ${mapping.chromeId}) 固定状态失败:`, error);
          failedCount++;

          // 如果标签页不存在，清理映射中的固定状态
          const errorMessage = error instanceof Error ? error.message : String(error);
          if (errorMessage.includes('No tab with id')) {
            await WorkonaTabManager.updateTabMetadata(mapping.workonaId, {
              metadata: {
                ...mapping.metadata,
                source: mapping.metadata?.source || 'workspace_website',
                isPinned: false,
                unpinnedAt: Date.now()
              }
            });
            console.log(`🗑️ 清理无效标签页的固定状态: ${mapping.workonaId}`);
          }
        }
      }

      console.log(`✅ 工作区 "${workspace.name}" 用户固定状态恢复完成: 成功 ${restoredCount} 个, 失败 ${failedCount} 个`);
    } catch (error) {
      console.error('恢复工作区固定状态失败:', error);
    }
  }

  /**
   * 清理旧的基于 Chrome ID 的固定状态存储
   */
  private static async cleanupLegacyPinnedStates(workspace: WorkSpace): Promise<void> {
    try {
      const storageKey = `workspacePinnedTabIds_${workspace.id}`;

      // 检查是否存在旧的固定状态存储
      const result = await chrome.storage.local.get([storageKey]);
      const legacyPinnedTabIds = result[storageKey];

      if (legacyPinnedTabIds && Array.isArray(legacyPinnedTabIds) && legacyPinnedTabIds.length > 0) {
        console.log(`🗑️ 清理工作区 "${workspace.name}" 的旧固定状态存储: ${legacyPinnedTabIds.length} 个条目`);

        // 删除旧的存储
        await chrome.storage.local.remove([storageKey]);

        console.log(`✅ 已清理工作区 "${workspace.name}" 的旧固定状态存储`);
      }
    } catch (error) {
      console.warn('清理旧固定状态存储失败:', error);
    }
  }

  /**
   * 智能检查并自动打开工作区中缺失的网站（修复版）
   */
  private static async openWorkspaceWebsites(workspace: WorkSpace): Promise<OperationResult<void>> {
    try {
      console.log(`🔍 智能检查并打开工作区 ${workspace.name} 中缺失的网站`);

      // 如果工作区没有配置网站，处理空工作区情况
      if (!workspace.websites || workspace.websites.length === 0) {
        console.log(`⚠️ 工作区 "${workspace.name}" 没有配置任何网站，确保窗口安全`);

        // 空工作区不需要特殊处理，依赖strictWindowProtection保护窗口
        console.log('ℹ️ [WorkspaceSwitcher] 空工作区，无需特殊窗口保护处理');

        return { success: true };
      }

      // 添加延迟确保标签页移动操作完全完成
      console.log('⏳ 等待标签页移动操作完全完成...');
      await new Promise(resolve => setTimeout(resolve, 100));

      // 获取当前窗口的所有标签页
      const currentTabsResult = await TabManager.getCurrentWindowTabs();
      if (!currentTabsResult.success) {
        console.log('❌ 获取当前窗口标签页失败:', currentTabsResult.error);
        return { success: true }; // 不阻断流程
      }

      const currentTabs = currentTabsResult.data!;
      const currentUrls = currentTabs.map(tab => tab.url);
      console.log(`📋 当前窗口已有 ${currentTabs.length} 个标签页:`, currentUrls);

      // 检查每个工作区网站是否已存在（纯 Workona ID 识别）
      const missingWebsites = [];
      const existingTabs = [];

      for (const website of workspace.websites) {
        // 仅通过 Workona ID 查找现有的核心标签页
        const existingTab = await this.findExistingCoreTabByWorkonaId(workspace.id, website.id, currentTabs);

        if (!existingTab) {
          // 标签页不存在，需要创建
          missingWebsites.push(website);
          console.log(`🔍 发现缺失的网站: ${website.title} (${website.url})`);
        } else {
          // 标签页存在
          existingTabs.push({
            tab: existingTab,
            website: website
          });
          console.log(`✅ 网站已存在: ${website.title} - 通过 Workona ID 识别`);

          // 验证并同步现有标签页的 Workona ID 映射和固定状态
          try {
            const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(existingTab.id);
            if (workonaIdResult.success && workonaIdResult.data) {
              const pinnedStatus = existingTab.isPinned ? '(固定)' : '(非固定)';
              console.log(`✅ 现有标签页已有 Workona ID: ${workonaIdResult.data} ${pinnedStatus}`);

              // 不主动同步浏览器重启后的固定状态，保持现有元数据
              console.log(`ℹ️ 保持现有元数据，不同步浏览器级别的固定状态`);
            } else {
              console.warn(`⚠️ 现有标签页缺少 Workona ID 映射，尝试重新建立映射`);

              // 为现有标签页重新建立 Workona ID 映射
              const newWorkonaId = WorkonaTabManager.generateWorkonaTabId(workspace.id);
              await WorkonaTabManager.createTabIdMapping(
                newWorkonaId,
                existingTab.id,
                workspace.id,
                website.id,
                {
                  isWorkspaceCore: true,
                  source: 'session_restored'
                }
              );

              console.log(`✅ 重新建立 Workona ID 映射: ${newWorkonaId}`);
            }
          } catch (error) {
            console.error(`❌ 验证现有标签页 Workona ID 失败:`, error);
          }
        }
      }

      if (missingWebsites.length === 0) {
        console.log(`✅ 工作区 "${workspace.name}" 的所有网站都已打开，无需创建新标签页`);
        return { success: true };
      }

      console.log(`🚀 需要打开 ${missingWebsites.length} 个缺失的网站`);
      let successCount = 0;
      let failCount = 0;

      for (const website of missingWebsites) {
        try {
          console.log(`📝 正在创建工作区核心标签页: ${website.title} (${website.url})`);

          // Workona 风格：创建标签页并建立 Workona ID 映射
          const tab = await chrome.tabs.create({
            url: website.url,
            pinned: false, // Workona 风格：不使用固定状态
            active: false // 不立即激活，保持当前标签页活跃
          });

          if (tab.id) {
            // 为新标签页创建 Workona ID 映射
            const newWorkonaId = WorkonaTabManager.generateWorkonaTabId(workspace.id);
            const mappingResult = await WorkonaTabManager.createTabIdMapping(
              newWorkonaId,
              tab.id,
              workspace.id,
              website.id,
              {
                isWorkspaceCore: true,
                source: 'workspace_website'
              }
            );

            if (mappingResult.success) {
              console.log(`✅ 成功创建工作区核心标签页: ${website.title} (Workona ID: ${newWorkonaId})`);
              successCount++;
            } else {
              console.error(`❌ 创建 Workona ID 映射失败 ${website.title}:`, mappingResult.error);
              failCount++;
            }
          } else {
            console.error(`❌ 创建标签页失败 ${website.title}: 无标签页ID`);
            failCount++;
          }
        } catch (error) {
          console.error(`❌ 处理网站 ${website.title} 时出错:`, error);
          failCount++;
        }
      }

      console.log(`🎯 工作区 "${workspace.name}" 缺失网站打开完成: 成功 ${successCount} 个，失败 ${failCount} 个`);
      return { success: true };
    } catch (error) {
      console.error('❌ 自动打开缺失网站时出错:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to open workspace websites',
          details: error,
        },
      };
    }
  }



  /**
   * 重新分类临时标签页到当前工作区
   * 将之前没有工作区时创建的临时标签页重新分类到正确的工作区
   */
  private static async reclassifyTemporaryTabs(targetWorkspaceId: string): Promise<void> {
    try {
      console.log(`🔄 重新分类临时标签页到工作区: ${targetWorkspaceId}`);

      // 获取所有标签页映射
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        console.warn('获取标签页映射失败，跳过重新分类');
        return;
      }

      const mappings = mappingsResult.data!;
      const tempMappings = mappings.filter(m => m.workspaceId === 'temp-user-session');

      if (tempMappings.length === 0) {
        console.log('没有找到需要重新分类的临时标签页');
        return;
      }

      console.log(`🔍 找到 ${tempMappings.length} 个临时标签页需要重新分类`);

      // 获取当前窗口的所有标签页
      const currentWindow = await chrome.windows.getCurrent();
      const allTabs = await chrome.tabs.query({ windowId: currentWindow.id });

      let reclassifiedCount = 0;

      for (const mapping of tempMappings) {
        try {
          // 检查标签页是否仍然存在
          const chromeTab = allTabs.find(tab => tab.id === mapping.chromeId);
          if (!chromeTab) {
            // 标签页已关闭，删除映射
            await WorkonaTabManager.removeTabMapping(mapping.workonaId);
            console.log(`🗑️ 删除已关闭标签页的映射: ${mapping.workonaId}`);
            continue;
          }

          // 更新现有映射的工作区ID，而不是创建新的映射
          // 这样可以保持标签页的身份连续性，避免工作区切换时创建重复标签页
          const updateResult = await WorkonaTabManager.updateTabMetadata(mapping.workonaId, {
            // 保持原有的标签页类型和核心属性
            isWorkspaceCore: mapping.isWorkspaceCore,
            tabType: mapping.tabType,
            metadata: {
              ...mapping.metadata,
              // 更新工作区归属，但保持其他元数据
              source: mapping.metadata?.source || 'user_opened'
            }
          });

          // 同时更新映射中的工作区ID
          if (updateResult.success) {
            // 直接更新存储中的工作区ID
            const mappingsResult = await StorageManager.getTabIdMappings();
            if (mappingsResult.success) {
              const allMappings = mappingsResult.data!;
              const mappingIndex = allMappings.findIndex(m => m.workonaId === mapping.workonaId);
              if (mappingIndex !== -1) {
                allMappings[mappingIndex].workspaceId = targetWorkspaceId;
                allMappings[mappingIndex].lastSyncAt = Date.now();
                await StorageManager.saveTabIdMappings(allMappings);
                console.log(`🔄 更新标签页工作区归属: ${mapping.workonaId} -> 工作区 ${targetWorkspaceId}`);
              }
            }
          }

          if (updateResult.success) {
            reclassifiedCount++;
            console.log(`✅ 更新标签页工作区归属: ${chromeTab.title} (${mapping.workonaId} -> 工作区 ${targetWorkspaceId})`);
          } else {
            console.error(`❌ 更新标签页工作区归属失败: ${chromeTab.title}`, updateResult.error);
          }
        } catch (error) {
          console.error(`❌ 处理临时标签页映射时出错:`, error);
        }
      }

      console.log(`✅ 重新分类完成: ${reclassifiedCount} 个标签页已分类到工作区 ${targetWorkspaceId}`);
    } catch (error) {
      console.error('❌ 重新分类临时标签页失败:', error);
    }
  }

  /**
   * 获取当前活跃的工作区
   */
  static async getCurrentWorkspace(): Promise<OperationResult<WorkSpace | null>> {
    try {
      const activeIdResult = await StorageManager.getActiveWorkspaceId();
      if (!activeIdResult.success) {
        return { success: false, error: activeIdResult.error };
      }

      const activeId = activeIdResult.data;
      if (!activeId) {
        return { success: true, data: null };
      }

      const workspaceResult = await StorageManager.getWorkspace(activeId);
      if (!workspaceResult.success) {
        // 如果工作区不存在，清除活跃状态
        await StorageManager.setActiveWorkspaceId(null);
        return { success: true, data: null };
      }

      return { success: true, data: workspaceResult.data };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.STORAGE_ERROR,
          message: 'Failed to get current workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 智能检测当前应该激活的工作区（Workona 风格：完全基于 ID 映射和智能匹配）
   */
  static async detectActiveWorkspace(): Promise<OperationResult<WorkSpace | null>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;
      if (workspaces.length === 0) {
        return { success: true, data: null };
      }

      const activeTabResult = await TabManager.getActiveTab();
      if (!activeTabResult.success) {
        return { success: true, data: null };
      }

      const activeTab = activeTabResult.data!;

      // 阶段1：优先通过 Workona ID 映射查找（最高优先级）
      const workonaIdResult = await WorkonaTabManager.getWorkonaIdByChromeId(activeTab.id);
      if (workonaIdResult.success && workonaIdResult.data) {
        const workonaId = workonaIdResult.data;
        const workspaceId = workonaId.split('-')[1]; // 从 t-{workspaceId}-{uuid} 提取工作区ID

        const matchingWorkspace = workspaces.find(w => w.id === workspaceId);
        if (matchingWorkspace) {
          console.log(`🔗 通过 Workona ID 检测到活跃工作区: ${matchingWorkspace.name} (${workonaId})`);
          return { success: true, data: matchingWorkspace };
        } else {
          // 工作区不存在，清理无效映射
          await WorkonaTabManager.removeTabMapping(workonaId);
          console.log(`🗑️ 清理无效的 Workona ID 映射: ${workonaId}`);
        }
      }

      // 阶段2：使用增强的工作区标签页匹配器（自动创建映射）
      const { WorkspaceTabContentMatcher } = await import('./tabs');
      const matchResult = await WorkspaceTabContentMatcher.isWorkspaceTab(activeTab);
      if (matchResult.isMatch && matchResult.workspaceId) {
        const matchingWorkspace = workspaces.find(w => w.id === matchResult.workspaceId);
        if (matchingWorkspace) {
          console.log(`🏢 通过智能匹配检测到活跃工作区: ${matchingWorkspace.name} (置信度: ${matchResult.confidence})`);
          return { success: true, data: matchingWorkspace };
        }
      }

      // Workona 风格：不再使用传统 URL 匹配作为回退
      console.log(`ℹ️ 活跃标签页未找到对应的工作区: ${activeTab.url}`);
      console.log(`💡 提示：如需将此标签页纳入工作区管理，请手动添加到相应工作区`);

      return { success: true, data: null };
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to detect active workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 添加当前标签页到工作区
   */
  static async addCurrentTabToWorkspace(workspaceId: string): Promise<OperationResult<void>> {
    try {
      const activeTabResult = await TabManager.getActiveTab();
      if (!activeTabResult.success) {
        return { success: false, error: activeTabResult.error };
      }

      const activeTab = activeTabResult.data!;

      // 添加到工作区
      const addResult = await WorkspaceManager.addWebsite(
        workspaceId,
        activeTab.url,
        {
          title: activeTab.title,
          favicon: activeTab.favicon,
        }
      );

      if (addResult.success) {
        return { success: true };
      } else {
        return { success: false, error: addResult.error };
      }
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to add current tab to workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 快速切换到下一个工作区
   */
  static async switchToNextWorkspace(): Promise<OperationResult<void>> {
    try {
      const workspacesResult = await StorageManager.getWorkspaces();
      if (!workspacesResult.success) {
        return { success: false, error: workspacesResult.error };
      }

      const workspaces = workspacesResult.data!;
      if (workspaces.length === 0) {
        return {
          success: false,
          error: {
            code: ERROR_CODES.WORKSPACE_NOT_FOUND,
            message: 'No workspaces available',
          },
        };
      }

      const currentResult = await this.getCurrentWorkspace();
      if (!currentResult.success) {
        return { success: false, error: currentResult.error };
      }

      const currentWorkspace = currentResult.data;
      let nextIndex = 0;

      if (currentWorkspace) {
        const currentIndex = workspaces.findIndex(w => w.id === currentWorkspace.id);
        nextIndex = (currentIndex + 1) % workspaces.length;
      }

      const nextWorkspace = workspaces[nextIndex];
      return await this.switchToWorkspace(nextWorkspace.id);
    } catch (error) {
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to switch to next workspace',
          details: error,
        },
      };
    }
  }

  /**
   * 处理工作区切换时的用户标签页隐藏状态
   */
  private static async handleUserTabsVisibilityState(workspace: WorkSpace): Promise<OperationResult<void>> {
    try {
      console.log(`🔄 检查工作区 "${workspace.name}" 的用户标签页隐藏状态`);

      // 使用新的工作区用户标签页管理器
      const { WorkspaceUserTabsVisibilityManager } = await import('./tabs');
      const stateResult = await WorkspaceUserTabsVisibilityManager.getWorkspaceUserTabsState(workspace.id);

      if (!stateResult.success) {
        console.log('⚠️ 无法获取用户标签页隐藏状态，跳过处理');
        return { success: true };
      }

      const { isHidden, hiddenTabIds, pinnedTabIds } = stateResult.data!;

      if (isHidden && hiddenTabIds.length > 0) {
        console.log(`🔒 工作区 "${workspace.name}" 有 ${hiddenTabIds.length} 个隐藏的用户标签页，需要保持隐藏状态`);

        // 验证隐藏的标签页是否仍然存在
        const existingTabIds: number[] = [];
        for (const tabId of hiddenTabIds) {
          try {
            await chrome.tabs.get(tabId);
            existingTabIds.push(tabId);
          } catch {
            console.log(`⚠️ 隐藏的标签页 ${tabId} 已不存在`);
          }
        }

        // 如果有标签页已不存在，更新隐藏列表
        if (existingTabIds.length !== hiddenTabIds.length) {
          const removedTabIds = hiddenTabIds.filter(id => !existingTabIds.includes(id));
          console.log(`🗑️ 清理 ${removedTabIds.length} 个不存在的隐藏标签页`);

          if (existingTabIds.length === 0) {
            // 如果所有隐藏的标签页都不存在了，清除隐藏状态
            await WorkspaceUserTabsVisibilityManager.setWorkspaceUserTabsState(workspace.id, false, []);
            console.log(`✅ 工作区 "${workspace.name}" 的所有隐藏标签页都已不存在，清除隐藏状态`);
          } else {
            // 更新隐藏列表，保持固定状态信息
            const validPinnedTabIds = pinnedTabIds.filter(id => existingTabIds.includes(id));
            await WorkspaceUserTabsVisibilityManager.setWorkspaceUserTabsState(workspace.id, true, existingTabIds, validPinnedTabIds);
            console.log(`✅ 更新工作区 "${workspace.name}" 的隐藏标签页列表，现有 ${existingTabIds.length} 个`);
          }
        }

        // 确保隐藏的标签页不在主窗口中
        if (existingTabIds.length > 0) {
          const currentWindow = await chrome.windows.getCurrent();
          const currentTabs = await chrome.tabs.query({ windowId: currentWindow.id });
          const tabsToHide = currentTabs.filter(tab => existingTabIds.includes(tab.id!));

          if (tabsToHide.length > 0) {
            console.log(`📤 发现 ${tabsToHide.length} 个应该隐藏的标签页在主窗口中，移动到专用窗口`);
            const tabIdsToMove = tabsToHide.map(tab => tab.id!);

            const moveResult = await WindowManager.moveTabsToWorkspaceWindow(
              tabIdsToMove,
              `workspace-${workspace.id}-hidden-tabs`,
              `工作区 ${workspace.name} - 隐藏的用户标签页`
            );

            if (moveResult.success) {
              console.log(`✅ 成功移动 ${tabIdsToMove.length} 个标签页到专用窗口`);
            } else {
              console.error('❌ 移动隐藏标签页到专用窗口失败:', moveResult.error);
            }
          }
        }
      } else {
        console.log(`✅ 工作区 "${workspace.name}" 没有隐藏的用户标签页`);
      }

      return { success: true };
    } catch (error) {
      console.error('❌ 处理用户标签页隐藏状态时出错:', error);
      return {
        success: false,
        error: {
          code: ERROR_CODES.TAB_ERROR,
          message: 'Failed to handle user tabs visibility state',
          details: error,
        },
      };
    }
  }

  /**
   * 通知工作区切换完成，触发UI状态更新
   */
  private static async notifyWorkspaceSwitchComplete(workspaceId: string): Promise<void> {
    try {
      // 使用状态同步工具发送工作区切换完成事件
      const { WorkspaceStateSync } = await import('./workspaceStateSync');
      WorkspaceStateSync.sendWorkspaceStateUpdate(workspaceId, 'switch');

      // 同时发送用户标签页状态更新事件，确保UI立即更新计数
      WorkspaceStateSync.sendWorkspaceStateUpdate(workspaceId, 'userTabsVisibility');

      // 强制刷新实时监控系统的状态
      const { UserTabsRealTimeMonitor } = await import('./tabs');
      await UserTabsRealTimeMonitor.forceRefreshWorkspaceState(workspaceId);

      console.log(`📢 已发送工作区 ${workspaceId} 切换完成和用户标签页状态更新事件`);
    } catch (error) {
      console.error('通知工作区切换完成失败:', error);
      // 不抛出错误，避免影响工作区切换流程
    }
  }

  /**
   * 通过 Workona ID 查找现有的核心标签页
   */
  private static async findExistingCoreTabByWorkonaId(
    workspaceId: string,
    websiteId: string,
    currentTabs: TabInfo[]
  ): Promise<TabInfo | null> {
    try {
      // 获取所有 TabIdMapping
      const mappingsResult = await StorageManager.getTabIdMappings();
      if (!mappingsResult.success) {
        return null;
      }

      const mappings = mappingsResult.data!;

      // 查找对应的映射
      const mapping = mappings.find(m =>
        m.workspaceId === workspaceId &&
        m.websiteId === websiteId &&
        m.isWorkspaceCore
      );

      if (!mapping) {
        return null;
      }

      // 查找对应的 Chrome 标签页
      const tab = currentTabs.find(t => t.id === mapping.chromeId);
      if (tab) {
        // 为标签页添加 Workona ID 信息以便后续识别
        (tab as any).workonaId = mapping.workonaId;
        console.log(`🎯 通过 Workona ID 找到核心标签页: ${mapping.workonaId} <-> ${tab.id}`);
        return tab;
      }

      // 如果标签页不存在，清理无效映射
      await WorkonaTabManager.removeTabMapping(mapping.workonaId);
      console.log(`🗑️ 清理无效的标签页映射: ${mapping.workonaId}`);

      return null;
    } catch (error) {
      console.error('查找 Workona ID 对应标签页失败:', error);
      return null;
    }
  }



  // Workona 风格：移除传统 URL 匹配逻辑
}
